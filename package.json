{"name": "@sage/xtrem-root", "description": "Umbrella project for Xtrem development", "buildStamp": "2025-06-16T20:05:42.678Z", "version": "56.0.27", "license": "UNLICENSED", "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "workspaces": ["{platform,services,shopfloor,showcase-stock,showcase-sales,tools,x3-connector,x3-services,wh-services}/*/{*,*/api}"], "devDependencies": {"@monorepo-utils/workspaces-to-typescript-project-references": "^2.9.0", "@sage/xdev": "^3.3.4", "@turbo/codemod": "^1.13.2", "@types/inquirer": "^9.0.0", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-lib-instrument": "^1.7.4", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.1", "@types/js-yaml": "^4.0.6", "@types/node": "^22.10.2", "@types/react": "^18.3.3", "@types/react-copy-to-clipboard": "^5.0.0", "@types/react-dom": "^18.0.0", "@types/react-redux": "^7.1.5", "@types/redux-mock-store": "^1.0.0", "@types/semver": "^7.5.2", "@types/showdown": "^2.0.0", "@types/sinon": "^17.0.0", "@types/source-map-support": "^0.5.7", "@types/styled-components": "^5.1.29", "@types/styled-system": "^5.1.13", "@types/webpack": "5.28.5", "@types/yargs": "^17.0.24", "allure-commandline": "^2.29.0", "axe-core": "^4.7.1", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "node-mocks-http": "^1.9.0", "pretty-format": "29.7.0", "redux-mock-store": "^1.5.3", "rimraf": "^6.0.0", "ts-morph": "^24.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tsd": "^0.32.0", "tsx": "^4.15.7", "turbo": "1.13.2", "typescript-json-schema": "^0.65.0", "v8-to-istanbul": "^9.0.1"}, "scripts": {"bootstrap": "xdev post-install --check && xdev run lerna-cache/installed-modules-cache.sh --write && xdev run common/check-nodetools-version.sh", "build": "xdev run lerna-cache/build.sh", "build:binary": "XTREM_BUILD_BINARY=1 pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:ci": "XTREM_CI=1 pnpm build", "build:modified": "XTREM_MODIFIED_ONLY=1 pnpm build", "build:offline": "XTREM_OFFLINE=1 pnpm build", "build:platform": "XTREM_SCOPES='platform' pnpm build", "build:platform:dry": "turbo run build --filter='./platform/**' --dry=json", "build:platform:turbo": "turbo run build --filter='./platform/**'", "build:services": "XTREM_SCOPES='platform|services' pnpm build", "build:services:dry": "turbo run build --filter='./services/**' --dry=json", "build:services:turbo": "turbo run build --filter='./services/**'", "build:shopfloor": "XTREM_SCOPES='platform|shopfloor' pnpm build", "build:showcase": "XTREM_SCOPES='platform|showcase-(stock|sales)' pnpm build", "build:tools": "XTREM_SCOPES='platform|tools' pnpm build", "build:wh-services": "XTREM_SCOPES='platform|x3-services/platform|wh-services' pnpm build", "build:win": "lerna run build --concurrency=2 --stream", "build:x3-connector": "XTREM_SCOPES='platform|x3-connector' pnpm build", "build:x3-services": "XTREM_SCOPES='platform|x3-services' pnpm build", "ci-only:rename-latest-upgrades": "pwd=`pwd` && cd platform/system/xtrem-system && pnpm xtrem upgrade --rename-vlatest $pwd", "clean": "pnpm clean:artifacts && pnpm clean:packages && pnpm clean:tsbuildinfo && pnpm clean:root", "clean:artifacts": "lerna run clean --parallel || exit 0", "clean:install": "pnpm clean && pnpm install", "clean:logs": "find ./logs -name 'xtrem.server-*' -exec rm {} \\;", "clean:packages": "lerna clean --yes || rm -rf \"{platform,services}/*/*/node_modules\"", "clean:root": "rm -rf node_modules scripts/node_modules", "clean:tmp": "rm -rf **/tmp", "clean:tsbuildinfo": "rm -rf \"{platform,services,shopfloor,showcase-stock,showcase-sales,tools,x3-connector,x3-services,wh-services}/*/*/tsconfig.tsbuildinfo\"", "clear:cache:local": "rm -rf $(dirname $(mktemp -u))/xtrem-cache; find $(dirname $(mktemp -u)) -name 'xtrem-cache-*' -exec rm {} \\;", "coverage:collect": "ts-node --transpile-only scripts/test-coverage/coverage.ts", "esmconvert": "pnpm run --dir scripts esmconvert", "fix:root:package:json": "xdev run lerna-cache/fix-root-package-json.cjs", "generate:config:json:schema": "scripts/gen-config-json-schema.sh", "generate:csv-changelog": "./scripts/gen-changelog-csv.sh", "generate:dependencies": "ts-node --transpile-only --project scripts/documentation/tsconfig.json scripts/documentation/doc-node.ts", "generate:docker:pnpm:lock:files": "xdev run release/generate-docker-pnpm-lock.sh", "generate:docs:ui": "ts-node --transpile-only scripts/xtrem-ui-docs.ts", "generate:scenario:uuid": "ts-node --project platform/cli/xtrem-cli-atp --transpile-only platform/cli/xtrem-cli-atp/generate-scenario-uuid.ts", "get-folder": "xdev run lerna-cache/get-package-folder.sh", "install:ci": "XTREM_CI=1 pnpm install --frozen-lockfile", "install:offline": "XTREM_OFFLINE=1 pnpm install", "lint": "xdev run lerna-cache/lint.sh", "lint:ci": "XTREM_CI=1 pnpm lint", "lint:modified": "XTREM_MODIFIED_ONLY=1 pnpm lint", "lint:modified:shell": "git diff --name-only --diff-filter=AM -- '*.sh' | xargs -r shellcheck", "lint:offline": "XTREM_OFFLINE=1 pnpm lint", "lint:platform": "XTREM_SCOPES='platform' pnpm lint", "lint:services": "XTREM_SCOPES='services' pnpm lint", "lint:shopfloor": "XTREM_SCOPES='shopfloor' pnpm lint", "lint:tools": "XTREM_SCOPES='tools' pnpm lint", "lint:wh-services": "XTREM_SCOPES='wh-services' pnpm lint", "lint:win": "lerna run lint --since=refs/remotes/origin/master --concurrency=2 --no-sort --stream", "lint:x3-services": "XTREM_SCOPES='x3-services' pnpm lint", "merge-accessibility-result-files": "node scripts/merge-accessibility-test-result-files.js", "merge-progress-result-files": "node scripts/merge-progress-result-files.js", "merge-test-result-files": "node scripts/merge-test-result-files.js", "pnpm:devPreinstall": "node ./scripts/add-sageai-scope.js || true", "postgres:clean": "docker rm xtrem_postgres || exit 0", "postgres:reset": "pnpm postgres:stop && pnpm postgres:clean && pnpm postgres:setup", "postgres:setup": "POSTGRES_PASSWORD=secret xdev run postgres/deploy-postgres.sh --restart unless-stopped", "postgres:stop": "docker stop xtrem_postgres || exit 0", "postinstall": "pnpm bootstrap && pnpm ts:references:fix && pnpm prettify:modified:json", "preinstall": "(! command -v fnm) || fnm use --install-if-missing --silent-if-unchanged", "prepare:release:sql:files": "xdev run release/prepare-release-sql-files.sh", "prepare:release:version": "xdev releng version", "prettier": "prettier", "prettify:modified:json": "git diff --name-only --diff-filter=AM -- '*.json' | xargs -r pnpm prettier --write", "publish:api": "scripts/release/publish-api.sh", "publish:documentation": "scripts/documentation/documentation.sh", "publish:from:package": "lerna exec 'pnpm publish --no-git-checks' --stream", "release:jira": "ts-node --transpile-only scripts/release-jira.ts", "release:packages": "xdev releng release-packages", "sqs:clean": "xdev env sqs --clean || exit 0", "sqs:reset": "xdev env sqs --reset", "sqs:setup": "xdev env sqs --setup", "sqs:stop": "xdev env sqs --stop || exit 0", "sqs:sync": "xdev env sqs --sync", "test": "xdev run lerna-cache/test.sh", "test:ci": "XTREM_CI=1 pnpm test", "test:ci:integration": "XTREM_CI=1 pnpm test:integration", "test:ci:integration:services": "XTREM_CI=1 PATTERN='smoke-test-*.feature' XTREM_SCOPES='services' pnpm test:integration", "test:ci:integration:services:adapters": "XTREM_CI=1 PATTERN='smoke-test-pr-cd-*.feature' XTREM_SCOPES='services/adapters/' pnpm test:integration", "test:ci:integration:services:application": "XTREM_CI=1 PATTERN='smoke-test-pr-cd-*.feature' XTREM_SCOPES='services/applications/' pnpm test:integration", "test:ci:integration:services:shared": "XTREM_CI=1 PATTERN='smoke-test-pr-cd-*.feature' XTREM_SCOPES='services/shared/' pnpm test:integration", "test:integration": "xdev run lerna-cache/test-integration.sh", "test:modified": "XTREM_MODIFIED_ONLY=1 pnpm test", "test:offline": "XTREM_OFFLINE=1 pnpm test", "test:platform-back-end": "XTREM_SCOPES='platform/back-end|platform/cli|platform/shared|platform/system|platform/show-case' pnpm test", "test:platform-front-end": "XTREM_SCOPES='platform/front-end' pnpm test", "test:services": "XTREM_SCOPES='services' pnpm test", "test:shopfloor": "XTREM_SCOPES='shopfloor' pnpm test", "test:showcase-sales": "XTREM_SCOPES='showcase-sales' pnpm test", "test:showcase-stock": "XTREM_SCOPES='showcase-stock' pnpm test", "test:smoke:ci": "XTREM_CI=1 XTREM_SCOPES='services' xdev run lerna-cache/test-integration-smoke.sh", "test:tools": "XTREM_SCOPES='tools' pnpm test", "test:wh-services": "XTREM_SCOPES='wh-services/' pnpm test", "test:win": "lerna run test --concurrency=2 --no-sort --stream", "test:x3-connector": "XTREM_SCOPES='x3-connector/' pnpm test", "test:x3-services": "XTREM_SCOPES='x3-services/' pnpm test", "translation:extract": "ts-node --transpile-only scripts/translations-extract.ts", "translation:import": "ts-node --transpile-only scripts/translations-import.ts", "ts:references:check": "workspaces-to-typescript-project-references --check", "ts:references:fix": "workspaces-to-typescript-project-references && git diff --name-only --diff-filter=AM -- '**/tsconfig.json' | xargs -r pnpm prettier --write", "turbo:setup": "NODE_OPTIONS='--import=tsx' node scripts/turbo-token.ts", "typescript-json-schema": "typescript-json-schema", "up-deps": "pnpm update --no-save --lockfile-only --ignore-scripts --ignore-pnpmfile --recursive", "use-azure-token": "ts-node --transpile-only scripts/npm-token.ts"}, "dependencies": {"@prettier/sync": "^0.6.0", "@sage/cucumber-steps-parser": "^3.1.0", "@sage/design-tokens": "4.35.0", "@sage/eslint-plugin-redos": "^1.1.0", "@sage/handlebars-helpers": "^1.0.1", "@sage/visual-process-editor": "^1.11.4", "@types/react-grid-layout": "^1.3.5", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "@typescript-eslint/types": "^7.10.0", "@typescript-eslint/utils": "^7.10.0", "@vscode/codicons": "^0.0.36", "acorn": "^8.12.1", "ag-charts-community": "9.3.1", "ag-charts-react": "9.3.1", "axios": "^1.9.0", "c8": "^10.1.2", "chalk": "^4.0.0", "chokidar": "^4.0.0", "cucumber-tag-expressions": "^2.0.3", "date-fns": "^2.28.0", "decimal.js": "^10.4.3", "dotenv": "^16.0.3", "downshift": "6.1.3", "draft-js": "^0.11.5", "eslint": "^8.49.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^10.1.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-unused-imports": "^4.0.0", "fuzzy": "^0.1.3", "glob": "^11.0.0", "glob-parent": "^6.0.2", "handlebars": "^4.7.8", "handlebars-intl": "^1.1.2", "html-entities": "^2.3.3", "inquirer": "^9.3.7", "inquirer-autocomplete-prompt": "^3.0.0", "inquirer-checkbox-autocomplete-prompt": "^0.2.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^5.0.0", "istanbul-reports": "^3.1.5", "js-yaml": "^4.1.0", "json-to-graphql-query": "^2.2.5", "json5": "^2.2.3", "jszip": "^3.10.1", "lerna": "^6.5.1", "lighthouse": "12.6.1", "logform": "^2.5.1", "looks-same": "^9.0.0", "matcher": "^4.0.0", "memoize-one": "^6.0.0", "mnemonist": "^0.40.0", "moment": "^2.29.1", "moment-timezone": "^0.6.0", "nanoid": "^3.3.8", "pdf-lib": "^1.17.1", "pluralize": "^8.0.0", "postgres-array": "^3.0.2", "prettier": "^3.3.3", "puppeteer": "^24.6.1", "react": "^18.3.1", "react-copy-to-clipboard": "^5.0.1", "react-dom": "^18.3.1", "react-grid-layout": "1.5.1", "react-monaco-editor": "^0.58.0", "react-redux": "^9.0.0", "react-transition-group": "^4.2.1", "redux": "^4.0.4", "redux-responsive": "^4.3.8", "redux-thunk": "^2.3.0", "requireindex": "^1.0.0", "semver": "^7.6.3", "showdown": "^2.0.0", "sinon": "^19.0.0", "socket.io": "^4.7.5", "source-map-support": "^0.5.12", "stream-browserify": "^3.0.0", "terser-webpack-plugin": "^5.1.1", "timers-browserify": "^2.0.12", "toobusy-js": "^0.5.1", "triple-beam": "^1.4.1", "ts-essentials": "^10.0.0", "ts-morph": "^24.0.0", "tslib": "^2.5.0", "typescript": "~5.8.3", "url-search-params-polyfill": "^8.0.0", "usehooks-ts": "^2.6.0", "utility-types": "^3.4.1", "whatwg-url": "^11.0.0", "xml-formatter": "3.6.6", "xmlbuilder": "^15.1.1"}, "pnpm": {"overrides": {"@sage/design-tokens": "4.35.0", "@puppeteer/browsers@1.3.0>tar-fs": "^2.1.3", "@turbo/codemod@1>axios": "^0.30.0", "@types/react": "^18.3.3", "@wdio/globals": "8.12.1", "d3-color": "^3.1.0", "graphql": "16.1.0-experimental-stream-defer.6", "react-dom": "^18.3.1", "react": "^18.3.1", "sinon": "^19.0.0", "styled-components": "^5.3.11", "typescript": "~5.8.3", "webdriverio": "8.12.1", "webpack": "^5.95.0", "puppeteer-core>ws": "^8.17.1", "@cucumber/cucumber>semver": "^7.5.2", "newrelic": "12.10.0"}, "peerDependencyRules": {"allowAny": ["redux"], "allowedVersions": {"codemirror-graphql@2>@codemirror/language": "6"}, "ignoreMissing": ["ckeditor5"]}, "patchedDependencies": {"@storybook/addon-webpack5-compiler-swc@1.0.5": "patches/@<EMAIL>", "react-grid-layout": "patches/<EMAIL>", "@types/react-grid-layout": "patches/@<EMAIL>", "@ag-grid-community/core": "patches/@ag-grid-community__core.patch", "carbon-react@153.7.0": "patches/<EMAIL>"}, "onlyBuiltDependencies": ["@contrast/fn-inspect", "@newrelic/native-metrics", "@parcel/watcher", "@sage/bms-dashboard", "@sage/xdev", "@swc/core", "canvas", "core-js", "esbuild", "nx", "oracledb", "protobufjs", "puppeteer", "re2", "sharp"]}, "comments": {"patches": {"@storybook/addon-webpack5-compiler-swc@1.0.5": "see https://github.com/nodejs/node/issues/56127"}}, "engines": {"npm": "~11.4.0"}, "packageManager": "pnpm@10.12.1"}