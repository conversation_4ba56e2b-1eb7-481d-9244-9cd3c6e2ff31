{"@sage/xtrem-finance/accounting_engine__no_records_to_process_on_accounting_staging_table": "There are no records to process on {{documentType}} {{documentNumber}} to {{targetDocumentType}}", "@sage/xtrem-finance/accounting_engine__there_are_records_already_processed_on_accounting_staging_table": "There are records already processed on {{documentType}} {{documentNumber}} to {{targetDocumentType}}", "@sage/xtrem-finance/accounting_engine__there_are_records_to_be_processed_on_accounting_staging_table_to_documents_that_are_not_target_document_type": "There are records to be processed on {{documentType}} {{documentNumber}} that are not {{targetDocumentType}}", "@sage/xtrem-finance/accounting_engine__wrong_number_of_records": "Number of records on the accounting staging: {{recordCount}}; expected value: {{batchSize}}", "@sage/xtrem-finance/activity__accounting_interface_listener__name": "Accounting interface listener", "@sage/xtrem-finance/activity__accounting_staging__name": "Accounting staging", "@sage/xtrem-finance/activity__accounts_payable_invoice__name": "Accounts payable invoice", "@sage/xtrem-finance/activity__accounts_payable_open_item__name": "Accounts payable open item", "@sage/xtrem-finance/activity__accounts_receivable_advance__name": "Accounts receivable advance", "@sage/xtrem-finance/activity__accounts_receivable_invoice__name": "Accounts receivable invoice", "@sage/xtrem-finance/activity__accounts_receivable_open_item__name": "Accounts receivable open item", "@sage/xtrem-finance/activity__accounts_receivable_payment__name": "Accounts receivable payment", "@sage/xtrem-finance/activity__datev_export__name": "DATEV export", "@sage/xtrem-finance/activity__generate_journal_entries__name": "Generate journal entries", "@sage/xtrem-finance/activity__journal_entry__name": "Journal entry", "@sage/xtrem-finance/activity__journal_entry_inquiry__name": "Journal entry inquiry", "@sage/xtrem-finance/activity__payment__name": "Payment", "@sage/xtrem-finance/activity__receipt__name": "Receipt", "@sage/xtrem-finance/check-date-range": "The start date cannot be later than the end date.", "@sage/xtrem-finance/classes__localized-messages__cant_read_account": "The account {{account}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_accounting_staging_amount": "The accounting staging amount for the document number {{documentNumber}} {{documentNumberId}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_document_line": "The base document line {{baseDocumentLine}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_base_tax": "The {{baseTax}} tax line could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_customer": "The customer {{customer}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_financial_site": "The {{financialSite}} site could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_item": "The {{item}} item could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_journal_entry_type": "The journal entry type for the {{documentType}} with the document number {{documentNumber}} {{documentId}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_posting_class_ap_ar_invoice": "The posting class for the document {{documentType}} {{documentNumber}} and item {{itemId}} could not be found. The posting class type is: {{postingClassType}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_posting_class_journal_entry": "The posting class for the document {{documentType}} {{documentNumber}} could not be found. The posting class type is: {{postingClassType}}.", "@sage/xtrem-finance/classes__localized-messages__cant_read_stock_journal": "The {{stockJournal}} stock journal could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_supplier": "The {{supplier}} supplier could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_tax": "The {{tax}} tax could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_taxPostingClass": "The {{taxPostingClass}} tax posting class could not be found.", "@sage/xtrem-finance/classes__localized-messages__cant_read_transaction_currency": "The currency {{transactionCurrency}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__customer_posting_class_missing_on_customer": "The posting class is missing for the {{customer}} customer.", "@sage/xtrem-finance/classes__localized-messages__item_posting_class_missing_on_item": "The posting class is missing for the {{item}} item.", "@sage/xtrem-finance/classes__localized-messages__journal_to_be_created_has_no_lines": "The journal entry to be created has no lines.", "@sage/xtrem-finance/classes__localized-messages__no_bp_account": "{{documentType}} {{documentNumber}}: The account could not be determined for the {{businessPartner}} business partner.", "@sage/xtrem-finance/classes__localized-messages__no_documents_to_process": "No documents to process for document type {{documentType}}, target document type {{targetDocumentType}} and document number {{documentNumber}}.", "@sage/xtrem-finance/classes__localized-messages__no_invoice": "Invoice number {{documentNumber}} could not be found.", "@sage/xtrem-finance/classes__localized-messages__supplier_posting_class_missing_on_supplier": "The posting class is missing for the {{supplier}} supplier.", "@sage/xtrem-finance/classes__localized-messages__target_document_type_not_supported": "The {{targetDocumentType}} target document type is not supported.", "@sage/xtrem-finance/classes__localized-messages__tax_posting_class_missing_on_tax": "The posting class is missing for the {{tax}} tax.", "@sage/xtrem-finance/classes__localized-messages__unable_to_get_account": "The account cannot be determined.", "@sage/xtrem-finance/client_functions__record_common__different_currencies": "The chosen currency is different to the currency associated with the bank account.", "@sage/xtrem-finance/client_functions__record_common__invalid_date": "You need to select a date in the past.", "@sage/xtrem-finance/client_functions__record_common__negative_credit_amount_error": "Enter a credit amount greater than 0.", "@sage/xtrem-finance/client_functions__record_common__negative_payment_amount_error": "Enter a payment amount greater than 0.", "@sage/xtrem-finance/client_functions__record_common__wrong_amount_in_bank_currency": "The bank currency and calculated amounts are different. Bank amount: {{currencySymbol}}{{bankAmount}}, calculated amount: ({{currencySymbol}}{{amount}}).", "@sage/xtrem-finance/client_functions__record_common__wrong_credit_amount_error": "The credited amount needs to be less than the amount that's due.", "@sage/xtrem-finance/client_functions__record_common__wrong_payment_amount_error": "The invoice payment needs to be less than the amount that's due.", "@sage/xtrem-finance/client_functions__void_record__void_date_should_be_after_payment_date": "The void date needs to be after the payment date.", "@sage/xtrem-finance/data_types__datev_export_status_enum__name": "Datev export status enum", "@sage/xtrem-finance/document_type_not_supported": "Document type not supported: {{documentType}}", "@sage/xtrem-finance/enums__datev_export_status__draft": "Draft", "@sage/xtrem-finance/enums__datev_export_status__error": "Error", "@sage/xtrem-finance/enums__datev_export_status__exported": "Exported", "@sage/xtrem-finance/enums__datev_export_status__exportInProgress": "Export in progress", "@sage/xtrem-finance/enums__datev_export_status__extracted": "Extracted", "@sage/xtrem-finance/enums__datev_export_status__extractionInProgress": "Extraction in progress", "@sage/xtrem-finance/fail__bulk_open_item_payment_notification_description__view_link": "Batch task logs", "@sage/xtrem-finance/functions__accounting_engine__no_documents_to_be_processed": "No documents to be processed.", "@sage/xtrem-finance/functions__accounting_engine__processing": "{{i}}/{{numberToBeProcess}} processing {{documents}}", "@sage/xtrem-finance/functions__accounting_engine__to_be_process": "Number of documents to process: {{numberToBeProcess}}", "@sage/xtrem-finance/generate-journal-entry-date": "Enter a start date earlier than the end date.", "@sage/xtrem-finance/menu_item__datev-interface": "DATEV interface", "@sage/xtrem-finance/menu_item__payment-tracking": "Payment tracking", "@sage/xtrem-finance/node__accounting_staging__finish_create_journal": "Accounting staging: finish {{messageJournalCreated}}", "@sage/xtrem-finance/node__accounting_staging__start_create_journal": "Accounting staging start ", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_description": " Journal entries generated: {{journalsCreated}}", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title": "Journal entry generation batch task successful", "@sage/xtrem-finance/node__accounting_staging_journal_entry_client_notification_title_fail": "Journal entry generation batch task not successful", "@sage/xtrem-finance/node__accounting-interface-listener__finance_document_notification_resent": "The document is being reprocessed.", "@sage/xtrem-finance/node__accounts_payable_invoice__resend_notification_for_finance": "Resending finance notification for accounts payable invoice: {{apInvoiceNumber}}.", "@sage/xtrem-finance/node__accounts_receivable_invoice__resend_notification_for_finance": "Resending finance notification for accounts receivable invoice: {{arInvoiceNumber}}.", "@sage/xtrem-finance/node-extensions__base-payment-document-extension__voided": "The payment was voided.", "@sage/xtrem-finance/node-extensions__business_entity_extension__property__composedDescription": "Composed description", "@sage/xtrem-finance/node-extensions__item_extension__property__composedDescription": "Composed description", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__apOpenItem": "AP open item", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__arOpenItem": "AR open item", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__companyAmount": "Company amount", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__origin": "Origin", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalNodeFactory": "Original node factory", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__originalOpenItem": "Original open item", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmount": "Signed amount", "@sage/xtrem-finance/node-extensions__payment_document_line_extension__property__signedAmountBankCurrency": "Signed amount bank currency", "@sage/xtrem-finance/node-extensions__site_extension__property__composedDescription": "Composed description", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob": "Create journals from accounting staging job", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__failed": "Create journals from accounting staging job failed.", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounting_interface_listener__asyncMutation__createJournalsFromAccountingStagingJob__parameter__journalsCreatedData": "Journals created data", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging": "Create journals from accounting staging", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__failed": "Create journals from accounting staging failed.", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__createJournalsFromAccountingStaging__parameter__journalsCreatedData": "Journals created data", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument": "Retry finance document", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__failed": "Retry finance document failed.", "@sage/xtrem-finance/nodes__accounting_interface_listener__mutation__retryFinanceDocument__parameter__financeTransaction": "Finance transaction", "@sage/xtrem-finance/nodes__accounting_interface_listener__node_name": "Accounting interface listener", "@sage/xtrem-finance/nodes__accounts_open_item__node_name": "Accounts open item", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft": "The status is not {{draft}}. The accounts payable invoice cannot be posted.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__cant_post_ap_invoice_when_status_is_not_draft_nor_error": "The posting status is not {{draft}} or {{error}}. The accounts payable invoice cannot be posted.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post": "Post", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__failed": "Post failed.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__post__parameter__apInvoice": "Accounts payable invoice", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__mutation__resendNotificationForFinance__parameter__apInvoice": "AP invoice", "@sage/xtrem-finance/nodes__accounts_payable_invoice__node_name": "Accounts payable invoice", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__account": "Account", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplier": "Bill-by supplier", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierName": "Bill-by supplier name", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__billBySupplierTaxIdNumber": "Bill-by supplier tax ID number", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__documentDate": "Document date", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__dueDate": "Due date", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteName": "Financial site name", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__financialSiteTaxIdNumber": "Financial site tax ID number", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__fxRateDate": "Exchange rate date", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__internalFinanceIntegrationStatus": "Internal finance integration status", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__invoiceDate": "Invoice date", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__lines": "Lines", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__number": "Number", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__openItems": "Open items", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__origin": "Origin", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTerm": "Payment term", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__paymentTracking": "Payment tracking", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplier": "Pay-to supplier", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__payToSupplierLinkedAddress": "Pay-to supplier linked address", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDate": "Posting date", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingDetails": "Posting details", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__postingStatus": "Posting status", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentNumber": "Purchase document number", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__purchaseDocumentSysId": "Purchase document system ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__rateDescription": "Rate description", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__reference": "Reference", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__returnLinkedAddress": "Return linked address", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountExcludingTax": "Total company amount excluding tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyAmountIncludingTax": "Total company amount including tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalCompanyTaxAmount": "Total company tax amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalExemptAmount": "Total exempt amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxableAmount": "Total taxable amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__accounts_payable_invoice__property__type": "Type", "@sage/xtrem-finance/nodes__accounts_payable_invoice__type_invalid": "Select a purchase invoice or purchase credit memo as the accounts payable invoice type.", "@sage/xtrem-finance/nodes__accounts_payable_invoice__update_not_allowed_status_posted": "The accounts payable invoice is posted. You cannot update it.", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__node_name": "Accounts payable invoice line", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__account": "Account", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__accountingStagingLines": "Accounting staging lines", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountExcludingTax": "", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__amountIncludingTax": "", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__attributesAndDimensions": "Attributes and dimensions", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentId": "Document ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentLineType": "Document line type", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__documentNumber": "Document number", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__exemptAmount": "Exempt amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineAmountExcludingTax": "Line amount excluding tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineAmountIncludingTax": "Line amount including tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__lineType": "Line type", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__recipientSite": "Recipient site", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxableAmount": "Taxable amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmount": "Tax amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDate": "Tax date", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxDetail": "Tax detail", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__taxLineTaxAmount": "Tax line tax amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line__property__uiTaxes": "UI taxes", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__node_name": "Accounts payable invoice line dimension", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__amount": "Amount", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__analyticalData": "Analytical data", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__businessSite": "Business site", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__customer": "Customer", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension01": "Dimension 01", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension02": "Dimension 02", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension03": "Dimension 03", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension04": "Dimension 04", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension05": "Dimension 05", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension06": "Dimension 06", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension07": "Dimension 07", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension08": "Dimension 08", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension09": "Dimension 09", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension10": "Dimension 10", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension11": "Dimension 11", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension12": "Dimension 12", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension13": "Dimension 13", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension14": "Dimension 14", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension15": "Dimension 15", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension16": "Dimension 16", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension17": "Dimension 17", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension18": "Dimension 18", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension19": "Dimension 19", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__dimension20": "Dimension 20", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__employee": "Employee", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Has attributes or dimensions changed", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__manufacturingSite": "Manufacturing site", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__originLine": "Origin line", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__project": "Project", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__stockSite": "Stock site", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__supplier": "Supplier", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_dimension__property__task": "Task", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__node_name": "Accounts payable invoice line tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_line_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__node_name": "Accounts payable invoice tax", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_payable_invoice_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_payable_open_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate": "Bulk open item update", "@sage/xtrem-finance/nodes__accounts_payable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Bulk open item update failed.", "@sage/xtrem-finance/nodes__accounts_payable_open_item__node_name": "Accounts payable open item", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__accountsPayableInvoice": "Accounts payable invoice", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__amountDue": "Amount due", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__businessRelation": "Business relation", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__paymentTracking": "Payment tracking", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__remainingCompanyAmount": "Remaining company amount", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__supplier": "Supplier", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalAmount": "Total amount", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmount": "Total company amount", "@sage/xtrem-finance/nodes__accounts_payable_open_item__property__totalCompanyAmountPaid": "Total company amount paid", "@sage/xtrem-finance/nodes__accounts_payable_open_item__skipped": "Document skipped as it is already fully paid: {{number}}.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_advance__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance__cant_post_ar_advance_when_status_is_not_draft_nor_error": "The posting status needs to be 'Draft' or 'Error'. The accounts receivable advance cannot be posted.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post": "Post", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__failed": "Post failed.", "@sage/xtrem-finance/nodes__accounts_receivable_advance__mutation__post__parameter__arAdvance": "Accounts receivable advance", "@sage/xtrem-finance/nodes__accounts_receivable_advance__node_name": "Accounts receivable advance", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceAmount": "Advance amount", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__advanceCompanyAmount": "Advance company amount", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__bankAccount": "Bank account", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__financialSiteName": "Financial site name", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__fxRateDate": "Exchange rate date", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__internalFinanceIntegrationStatus": "Internal finance integration status", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__lines": "Lines", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__number": "Number", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__paymentDate": "Payment date", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerId": "Pay-to customer ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__payToCustomerName": "Pay-to customer name", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingDate": "Posting date", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__postingStatus": "Posting status", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__reference": "Reference", "@sage/xtrem-finance/nodes__accounts_receivable_advance__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__accounts_receivable_advance__update_not_allowed_status_posted": "You can only update an accounts receivable advance that is a draft.", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__node_name": "Accounts receivable advance line", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__account": "Account", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceAmount": "Advance amount", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__advanceCompanyAmount": "Advance company amount", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__analyticalData": "Analytical data", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__businessSite": "Business site", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__customer": "Customer", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension01": "Dimension 01", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension02": "Dimension 02", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension03": "Dimension 03", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension04": "Dimension 04", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension05": "Dimension 05", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension06": "Dimension 06", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension07": "Dimension 07", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension08": "Dimension 08", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension09": "Dimension 09", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension10": "Dimension 10", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension11": "Dimension 11", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension12": "Dimension 12", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension13": "Dimension 13", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension14": "Dimension 14", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension15": "Dimension 15", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension16": "Dimension 16", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension17": "Dimension 17", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension18": "Dimension 18", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension19": "Dimension 19", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__dimension20": "Dimension 20", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentId": "Document ID", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__documentNumber": "Document number", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__employee": "Employee", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__hasAttributesOrDimenionsChanged": "Has attributes or dimensions changed", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__manufacturingSite": "Manufacturing site", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__project": "Project", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__stockSite": "Stock site", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__supplier": "Supplier", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__task": "Task", "@sage/xtrem-finance/nodes__accounts_receivable_advance_line__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__cant_post_ar_invoice_when_status_is_not_draft_nor_error": "The posting status is not {{draft}} or {{error}}. The accounts receivable invoice cannot be posted.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__information_payment_tracking": "Update the open items previously created according to the AP/AR aging balance", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post": "Post", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__failed": "Post failed.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__post__parameter__arInvoice": "Accounts receivable invoice", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__mutation__resendNotificationForFinance__parameter__arInvoice": "AR invoice", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__node_name": "Accounts receivable invoice", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__payment_tracking": "Payment tracking service option activated", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__account": "Account", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomer": "Bill-to customer", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerName": "Bill-to customer name", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__billToCustomerTaxIdNumber": "Bill-to customer tax ID number", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__documentDate": "Document date", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__dueDate": "Due date", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteName": "Financial site name", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__financialSiteTaxIdNumber": "Financial site tax ID number", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__fxRateDate": "Exchange rate date", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__internalFinanceIntegrationStatus": "Internal finance integration status", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__invoiceDate": "Invoice date", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__isPrinted": "Printed", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__lines": "Lines", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__number": "Number", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__openItems": "Open items", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__origin": "Origin", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentStatus": "Payment status", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__paymentTerm": "Payment term", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDate": "Posting date", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingDetails": "Posting details", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__postingStatus": "Posting status", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__rateDescription": "Rate description", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__reference": "Reference", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentNumber": "Sales document number", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__salesDocumentSysId": "Sales document System ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxEngine": "Tax engine", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountExcludingTax": "Total company amount excluding tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyAmountIncludingTax": "Total company amount including tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalCompanyTaxAmount": "Total company tax amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalExemptAmount": "Total exempt amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxableAmount": "Total taxable amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmount": "Total tax amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__property__type": "Type", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__type_invalid": "Select a sales invoice or sales credit memo as the accounts receivable invoice type.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice__update_not_allowed_status_posted": "The accounts receivable invoice is posted. You cannot update it.", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__node_name": "Accounts receivable invoice line", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__account": "Account", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__accountingStagingLines": "Accounting staging lines", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountExcludingTax": "", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__amountIncludingTax": "", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__attributesAndDimensions": "Attributes and dimensions", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentId": "Document ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentLineType": "Document line type", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__documentNumber": "Document number", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__exemptAmount": "Exempt amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineAmountExcludingTax": "Line amount excluding tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineAmountIncludingTax": "Line amount including tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__lineType": "Line type", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__netPriceIncludingTax": "Net price including tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__providerSite": "Provider site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantity": "Quantity", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__quantityInSalesUnit": "Quantity in sales unit", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxableAmount": "Taxable amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmount": "Tax amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDate": "Tax date", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxDetail": "Tax detail", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxes": "Taxes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__taxLineTaxAmount": "Tax line tax amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line__property__uiTaxes": "UI taxes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__node_name": "Accounts receivable invoice line dimension", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__amount": "Amount", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__analyticalData": "Analytical data", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__businessSite": "Business site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__customer": "Customer", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension01": "Dimension 01", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension02": "Dimension 02", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension03": "Dimension 03", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension04": "Dimension 04", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension05": "Dimension 05", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension06": "Dimension 06", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension07": "Dimension 07", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension08": "Dimension 08", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension09": "Dimension 09", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension10": "Dimension 10", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension11": "Dimension 11", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension12": "Dimension 12", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension13": "Dimension 13", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension14": "Dimension 14", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension15": "Dimension 15", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension16": "Dimension 16", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension17": "Dimension 17", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension18": "Dimension 18", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension19": "Dimension 19", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__dimension20": "Dimension 20", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__employee": "Employee", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__hasAttributesOrDimenionsChanged": "Has attributes or dimensions changed", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__manufacturingSite": "Manufacturing site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__originLine": "Origin line", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__project": "Project", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__stockSite": "Stock site", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__supplier": "Supplier", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_dimension__property__task": "Task", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__node_name": "Accounts receivable invoice line tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_line_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__node_name": "Accounts receivable invoice tax", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_invoice_tax__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate": "Bulk open item update", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__bulkMutation__bulkOpenItemUpdate__failed": "Bulk open item update failed.", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__node_name": "Accounts receivable open item", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__accountsReceivableInvoice": "Accounts receivable invoice", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__amountDue": "Amount due", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__businessRelation": "Business relation", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__receipts": "Receipts", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__remainingCompanyAmount": "Remaining company amount", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalAmount": "Total amount", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmount": "Total company amount", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__property__totalCompanyAmountPaid": "Total company amount paid", "@sage/xtrem-finance/nodes__accounts_receivable_open_item__skipped": "Document skipped as it is already fully paid: {{number}}.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_payment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment__cant_post_ar_payment_when_status_is_not_draft_nor_error": "The posting status needs to be 'Draft' or 'Error'. The accounts receivable payment cannot be posted.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post": "Post", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__failed": "Post failed.", "@sage/xtrem-finance/nodes__accounts_receivable_payment__mutation__post__parameter__arPayment": "Accounts receivable payment", "@sage/xtrem-finance/nodes__accounts_receivable_payment__node_name": "Accounts receivable payment", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__bankAccount": "Bank account", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__description": "Description", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__financialSiteName": "Financial site name", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__fxRateDate": "Exchange rate date", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__internalFinanceIntegrationStatus": "Internal finance integration status", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__lines": "Lines", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__number": "Number", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentAmount": "Payment amount", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentCompanyAmount": "Payment company amount", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__paymentDate": "Payment date", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerId": "Pay-to customer ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__payToCustomerName": "Pay-to customer name", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingDate": "Posting date", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__postingStatus": "Posting status", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__reference": "Reference", "@sage/xtrem-finance/nodes__accounts_receivable_payment__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__accounts_receivable_payment__update_not_allowed_status_posted": "The accounts receivable payment status is {{status}}. You cannot update it.", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__node_name": "Accounts receivable payment line", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__document": "Document", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentId": "Document ID", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__documentNumber": "Document number", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentAmount": "Payment amount", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__paymentCompanyAmount": "Payment company amount", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__property__type": "Type", "@sage/xtrem-finance/nodes__accounts_receivable_payment_line__type_invalid": "The accounts receivable payment line type must be 'Sales invoice' or 'Sales credit memo'.", "@sage/xtrem-finance/nodes__ap_invoice__posted": "The accounts payable invoice was posted.", "@sage/xtrem-finance/nodes__ar_advance__posted": "The accounts receivable advance was posted.", "@sage/xtrem-finance/nodes__ar_invoice__posted": "The accounts receivable invoice was posted.", "@sage/xtrem-finance/nodes__ar_payment__posted": "The accounts receivable payment was posted.", "@sage/xtrem-finance/nodes__base_open_item__text_forced_close": "Forced close on {{date}} by {{user}}", "@sage/xtrem-finance/nodes__base_payment_document_payment_already_voided": "The payment was already voided.", "@sage/xtrem-finance/nodes__datev_export__account_without_datev_id": "Account without a DATEV ID: {{account}}.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport": "DATEV export", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__failed": "DATEV export failed.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction": "DATEV extraction", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__failed": "DATEV extraction failed.", "@sage/xtrem-finance/nodes__datev_export__asyncMutation__datevExtraction__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export__business_relation_without_datev_id": "Business entity without a DATEV ID: {{businessEntity}}.", "@sage/xtrem-finance/nodes__datev_export__complete_account_export": "DATEV account export completed successfully.", "@sage/xtrem-finance/nodes__datev_export__complete_business_relation_export": "DATEV business relation export completed successfully.", "@sage/xtrem-finance/nodes__datev_export__complete_export": "DATEV export completed successfully.", "@sage/xtrem-finance/nodes__datev_export__complete_journal_entry_lines_export": "DATEV journal entry line export completed successfully.", "@sage/xtrem-finance/nodes__datev_export__datev_file": "DATEV export files", "@sage/xtrem-finance/nodes__datev_export__datev_file_ready": "Files created: {{nbOfFilesCreated}}.\n", "@sage/xtrem-finance/nodes__datev_export__datev_number_of_warnings": "Warnings: {{nbOfWarnings}}.", "@sage/xtrem-finance/nodes__datev_export__end_extraction": "DATEV extraction completed.", "@sage/xtrem-finance/nodes__datev_export__failure_description": "DATEV extraction failed to complete successfully. Error: {{error}}", "@sage/xtrem-finance/nodes__datev_export__failure_description_export": "DATEV export failed. Error: {{error}}", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_account_without_datev_id": "Journal entry line for an account without a DATEV ID. Journal entry {{journalEntryNumber}}, account {{account}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_contra_account_without_datev_id": "Journal entry line for a contra account without a DATEV ID. Journal entry {{journalEntryNumber}}, contra account {{contraAccount}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_no_tax_line_for_taxable_amount": "A tax line cannot be found for the taxable amount. Journal entry {{journalEntryNumber}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_with_different_tax": "Journal entry line with an automatic account using a different tax code. Journal entry {{journalEntryNumber}}, account {{account}}.", "@sage/xtrem-finance/nodes__datev_export__journal_entry_line_without_posting_key": "Journal entry line with a non-automatic account and no posting key. Journal entry {{journalEntryNumber}}, account {{account}}.", "@sage/xtrem-finance/nodes__datev_export__node_name": "DATEV export", "@sage/xtrem-finance/nodes__datev_export__number_extracted_accounts": "DATEV accounts extracted: {{numberExtractedAccounts}}.", "@sage/xtrem-finance/nodes__datev_export__number_extracted_customers_suppliers": "DATEV customers and suppliers extracted: {{numberExtractedBusinessRelations}}.", "@sage/xtrem-finance/nodes__datev_export__number_extracted_journal_entry_lines": "DATEV journal entry lines extracted: {{numberExtractedJournalEntryLines}}.", "@sage/xtrem-finance/nodes__datev_export__property__accountsWithoutDatevId": "Accounts without DATEV ID", "@sage/xtrem-finance/nodes__datev_export__property__attributeType1": "Attribute type 1", "@sage/xtrem-finance/nodes__datev_export__property__attributeType2": "Attribute type 2", "@sage/xtrem-finance/nodes__datev_export__property__company": "Company", "@sage/xtrem-finance/nodes__datev_export__property__customersWithoutDatevId": "Customers without DATEV ID", "@sage/xtrem-finance/nodes__datev_export__property__dateRange": "Date range", "@sage/xtrem-finance/nodes__datev_export__property__datevConsultantNumber": "DATEV consultant number", "@sage/xtrem-finance/nodes__datev_export__property__datevCustomerNumber": "DATEV customer number", "@sage/xtrem-finance/nodes__datev_export__property__datevExportAccounts": "DATEV export accounts", "@sage/xtrem-finance/nodes__datev_export__property__datevExportBusinessRelations": "DATEV export business relations", "@sage/xtrem-finance/nodes__datev_export__property__datevExportJournalEntryLines": "DATEV export journal entry lines", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType1": "Dimension type 1", "@sage/xtrem-finance/nodes__datev_export__property__dimensionType2": "Dimension type 2", "@sage/xtrem-finance/nodes__datev_export__property__doAccounts": "Do accounts", "@sage/xtrem-finance/nodes__datev_export__property__doCustomersSuppliers": "Do customers suppliers", "@sage/xtrem-finance/nodes__datev_export__property__doJournalEntries": "Do journal entries", "@sage/xtrem-finance/nodes__datev_export__property__endDate": "End date", "@sage/xtrem-finance/nodes__datev_export__property__fiscalYearStart": "Fiscal year start", "@sage/xtrem-finance/nodes__datev_export__property__id": "ID", "@sage/xtrem-finance/nodes__datev_export__property__isLocked": "Is locked", "@sage/xtrem-finance/nodes__datev_export__property__skrCoa": "SKR COA", "@sage/xtrem-finance/nodes__datev_export__property__startDate": "Start date", "@sage/xtrem-finance/nodes__datev_export__property__status": "Status", "@sage/xtrem-finance/nodes__datev_export__property__suppliersWithoutDatevId": "Suppliers without DATEV ID", "@sage/xtrem-finance/nodes__datev_export__property__timeStamp": "Time stamp", "@sage/xtrem-finance/nodes__datev_export__start_export": "DATEV export started.", "@sage/xtrem-finance/nodes__datev_export__start_extract_accounts": "DATEV account extraction start.", "@sage/xtrem-finance/nodes__datev_export__start_extract_customers_suppliers": "DATEV customer and supplier extraction start.", "@sage/xtrem-finance/nodes__datev_export__start_extract_journal_entry_lines": "DATEV journal entry lines extraction start.", "@sage/xtrem-finance/nodes__datev_export__start_extraction": "DATEV extraction started.", "@sage/xtrem-finance/nodes__datev_export__stop_extraction": "Stop DATEV extraction on {{stopDate}}.", "@sage/xtrem-finance/nodes__datev_export__success_description": "DATEV extraction completed successfully.", "@sage/xtrem-finance/nodes__datev_export__success_notification_title": "DATEV extraction complete", "@sage/xtrem-finance/nodes__datev_export__user_download_accounts": "Download accounts", "@sage/xtrem-finance/nodes__datev_export__user_download_journal_entry_lines": "Download journal entry lines", "@sage/xtrem-finance/nodes__datev_export__user_notifications_download_customers_and_suppliers": "Download customers and suppliers", "@sage/xtrem-finance/nodes__datev_export__user_notifications_history": "History", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export_account__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export_account__node_name": "DATEV export account", "@sage/xtrem-finance/nodes__datev_export_account__property__account": "Account", "@sage/xtrem-finance/nodes__datev_export_account__property__datevExport": "DATEV export", "@sage/xtrem-finance/nodes__datev_export_account__property__datevId": "DATEV ID", "@sage/xtrem-finance/nodes__datev_export_account__property__name": "Name", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export_business_relation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export_business_relation__node_name": "DATEV export business relation", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__businessRelation": "Business relation", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__city": "City", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__country": "Country", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevExport": "DATEV export", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__datevId": "DATEV ID", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__name": "Name", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__postcode": "Postcode", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__street": "Street", "@sage/xtrem-finance/nodes__datev_export_business_relation__property__taxIdNumber": "Tax ID number", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__node_name": "DATEV export journal entry line", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__businessEntityTaxIdNumber": "Business entity tax ID number", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__companyValue": "Company value", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevAccountId": "DATEV account ID", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevContraAccountId": "DATEV contra account ID", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevExport": "DATEV export", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__datevSign": "DATEV sign", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__description": "Description", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension1": "Dimension 1", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__dimension2": "Dimension 2", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__journalEntryLine": "Journal entry line", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__locked": "Locked", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__number": "Number", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingDate": "Posting date", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__postingKey": "Posting key", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__siteTaxIdNumber": "Site tax ID number", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__datev_export_journal_entry_line__property__transactionValue": "Transaction value", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file": "DATEV file", "@sage/xtrem-finance/nodes__datev_export_listener__datev_file_ready": "File ready to download", "@sage/xtrem-finance/nodes__datev_export_listener__node_name": "DATEV export listener", "@sage/xtrem-finance/nodes__initialize_open_item__node_name": "Initialize open item", "@sage/xtrem-finance/nodes__intacct-bank-account-transaction_feed__site_legislation_and_coa_legislation_dont_match": "The site legislation must be the same as the chart of accounts legislation.", "@sage/xtrem-finance/nodes__journal_entry__ap_invoice_reference": "The accounts payable invoice reference can only be used in prejournals originating from an accounts payable invoice.", "@sage/xtrem-finance/nodes__journal_entry__ar_invoice_reference": "The accounts receivable invoice reference can only be used in prejournals originating from an accounts receivable invoice.", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry__contra_journal_entry_line_is_mandatory_on_new_documents": "You need to enter a contra journal entry on the line.", "@sage/xtrem-finance/nodes__journal_entry__document_type_not_allowed": "The document type can only be set for the French legislation.", "@sage/xtrem-finance/nodes__journal_entry__journal_entry_dimension_line_not_equal_to_journal_line": "The total attribute/dimension allocation amount is not equal to the line amount.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post": "Post", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__failed": "Post failed.", "@sage/xtrem-finance/nodes__journal_entry__mutation__post__parameter__journalEntry": "Journal entry", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_account": "Required account dimensions [{{dimensions}}] have not been declared for account {{lineAccount}} on journal line {{lineCount}}.", "@sage/xtrem-finance/nodes__journal_entry__no_attribute_value_company": "Required company dimensions [{{dimensions}}] have not been declared for account {{lineAccount}} on journal line {{lineCount}}.", "@sage/xtrem-finance/nodes__journal_entry__no_sequence_number": "The journal entry sequence number cannot be generated. Enter a default sequence number first.", "@sage/xtrem-finance/nodes__journal_entry__node_name": "Journal entry", "@sage/xtrem-finance/nodes__journal_entry__number_change_forbidden": "You can only edit the journal entry number on new records.", "@sage/xtrem-finance/nodes__journal_entry__property__apInvoice": "AP invoice", "@sage/xtrem-finance/nodes__journal_entry__property__arInvoice": "AR invoice", "@sage/xtrem-finance/nodes__journal_entry__property__description": "Description", "@sage/xtrem-finance/nodes__journal_entry__property__documentType": "Document type", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationApp": "Finance integration application", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppRecordId": "Finance integration application record ID", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationAppUrl": "Finance integration application URL", "@sage/xtrem-finance/nodes__journal_entry__property__financeIntegrationStatus": "Finance integration status", "@sage/xtrem-finance/nodes__journal_entry__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__journal_entry__property__internalFinanceIntegrationStatus": "Internal finance integration status", "@sage/xtrem-finance/nodes__journal_entry__property__journal": "Journal", "@sage/xtrem-finance/nodes__journal_entry__property__lines": "Lines", "@sage/xtrem-finance/nodes__journal_entry__property__number": "Number", "@sage/xtrem-finance/nodes__journal_entry__property__origin": "Origin", "@sage/xtrem-finance/nodes__journal_entry__property__postingDate": "Posting date", "@sage/xtrem-finance/nodes__journal_entry__property__postingStatus": "Posting status", "@sage/xtrem-finance/nodes__journal_entry__property__reference": "Reference", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive": "Finance integration packages active", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__failed": "Are finance integration packages active failed.", "@sage/xtrem-finance/nodes__journal_entry__query__areFinanceIntegrationPackagesActive__parameter__dummy": "Dummy", "@sage/xtrem-finance/nodes__journal_entry__reconcile_journal_entry_by_key": "The journal entry is unbalanced for the {{financialSite}} financial site.", "@sage/xtrem-finance/nodes__journal_entry__two_journal_entry_lines_mandatory": "Enter at least two lines in the journal entry.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_inquiry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord": "Single record", "@sage/xtrem-finance/nodes__journal_entry_inquiry__mutation__singleRecord__failed": "Single record failed.", "@sage/xtrem-finance/nodes__journal_entry_inquiry__node_name": "Journal entry inquiry", "@sage/xtrem-finance/nodes__journal_entry_inquiry__property__journalEntryLines": "Journal entry lines", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_mandatory": "The {{account}} account is a control account. Enter a business entity.", "@sage/xtrem-finance/nodes__journal_entry_line__business_entity_reference_not_possible": "The {{account}} account is not a control account. You cannot enter a business entity.", "@sage/xtrem-finance/nodes__journal_entry_line__journal_entry_type_line_is_mandatory_on_new_documents": "You need to enter the journal entry type on the line.", "@sage/xtrem-finance/nodes__journal_entry_line__node_name": "Journal entry line", "@sage/xtrem-finance/nodes__journal_entry_line__property__account": "Account", "@sage/xtrem-finance/nodes__journal_entry_line__property__accountingStagingLines": "Accounting staging lines", "@sage/xtrem-finance/nodes__journal_entry_line__property__attributesAndDimensions": "Attributes and dimensions", "@sage/xtrem-finance/nodes__journal_entry_line__property__baseTax": "Base tax", "@sage/xtrem-finance/nodes__journal_entry_line__property__blank": "Blank", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessEntity": "Business entity", "@sage/xtrem-finance/nodes__journal_entry_line__property__businessSiteAttribute": "Business site attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__chartOfAccount": "Chart of accounts", "@sage/xtrem-finance/nodes__journal_entry_line__property__commonReference": "Common reference", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyAmount": "Company amount", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCredit": "Company credit", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyDebit": "Company debit", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRate": "Company exchange rate", "@sage/xtrem-finance/nodes__journal_entry_line__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraAccount": "Contra account", "@sage/xtrem-finance/nodes__journal_entry_line__property__contraJournalEntryLine": "Contra journal entry line", "@sage/xtrem-finance/nodes__journal_entry_line__property__customerAttribute": "Customer attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevBusinessEntityTaxIdNumber": "DATEV business entity tax ID number", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevCompanyAmount": "DATEV company amount", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevContraAccountId": "DATEV contra account ID", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevPostingKey": "DATEV posting key", "@sage/xtrem-finance/nodes__journal_entry_line__property__datevTransactionAmount": "DATEV transaction amount", "@sage/xtrem-finance/nodes__journal_entry_line__property__deductibleTaxRate": "Deductible tax rate", "@sage/xtrem-finance/nodes__journal_entry_line__property__description": "Description", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension01": "Dimension 01", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension02": "Dimension 02", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension03": "Dimension 03", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension04": "Dimension 04", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension05": "Dimension 05", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension06": "Dimension 06", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension07": "Dimension 07", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension08": "Dimension 08", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension09": "Dimension 09", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension10": "Dimension 10", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension11": "Dimension 11", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension12": "Dimension 12", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension13": "Dimension 13", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension14": "Dimension 14", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension15": "Dimension 15", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension16": "Dimension 16", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension17": "Dimension 17", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension18": "Dimension 18", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension19": "Dimension 19", "@sage/xtrem-finance/nodes__journal_entry_line__property__dimension20": "Dimension 20", "@sage/xtrem-finance/nodes__journal_entry_line__property__dueDate": "Due date", "@sage/xtrem-finance/nodes__journal_entry_line__property__employeeAttribute": "Employee attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAmount": "Financial site amount", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteAttribute": "Financial site attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCredit": "Financial site credit", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteCurrency": "Financial site currency", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteDebit": "Financial site debit", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRate": "Financial site exchange rate", "@sage/xtrem-finance/nodes__journal_entry_line__property__financialSiteFxRateDivisor": "Financial site exchange rate divisor", "@sage/xtrem-finance/nodes__journal_entry_line__property__fxRateDate": "Exchange rate date", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryDescription": "Inquiry description", "@sage/xtrem-finance/nodes__journal_entry_line__property__inquiryTransactionCurrency": "Inquiry transaction currency", "@sage/xtrem-finance/nodes__journal_entry_line__property__isBalanceLine": "Is balance line", "@sage/xtrem-finance/nodes__journal_entry_line__property__itemAttribute": "Item attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntry": "Journal entry", "@sage/xtrem-finance/nodes__journal_entry_line__property__journalEntryTypeLine": "Journal entry type line", "@sage/xtrem-finance/nodes__journal_entry_line__property__manufacturingSiteAttribute": "Manufacturing site attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__numericSign": "Numeric sign", "@sage/xtrem-finance/nodes__journal_entry_line__property__projectAttribute": "Project attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__rateDescription": "Rate description", "@sage/xtrem-finance/nodes__journal_entry_line__property__sign": "Sign", "@sage/xtrem-finance/nodes__journal_entry_line__property__signedTransactionAmount": "Signed transaction amount", "@sage/xtrem-finance/nodes__journal_entry_line__property__stockSiteAttribute": "Stock site attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierAttribute": "Supplier attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-finance/nodes__journal_entry_line__property__taskAttribute": "Task attribute", "@sage/xtrem-finance/nodes__journal_entry_line__property__tax": "Tax", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxDate": "Tax date", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxExternalReference": "Tax external reference", "@sage/xtrem-finance/nodes__journal_entry_line__property__taxRate": "Tax rate", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionAmount": "Transaction amount", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCredit": "Transaction credit", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__journal_entry_line__property__transactionDebit": "Transaction debit", "@sage/xtrem-finance/nodes__journal_entry_line__property__validationDate": "Validation date", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_empty": "Leave the tax date empty.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_date_mandatory": "Enter a tax date.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_empty": "Leave the tax reference empty.", "@sage/xtrem-finance/nodes__journal_entry_line__tax_ref_mandatory": "Enter a tax reference.", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__node_name": "Journal entry line dimension", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__analyticalData": "Analytical data", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__businessSite": "Business site", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyAmount": "Company amount", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__companyCurrency": "Company currency", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__customer": "Customer", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension01": "Dimension 01", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension02": "Dimension 02", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension03": "Dimension 03", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension04": "Dimension 04", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension05": "Dimension 05", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension06": "Dimension 06", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension07": "Dimension 07", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension08": "Dimension 08", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension09": "Dimension 09", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension10": "Dimension 10", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension11": "Dimension 11", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension12": "Dimension 12", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension13": "Dimension 13", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension14": "Dimension 14", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension15": "Dimension 15", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension16": "Dimension 16", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension17": "Dimension 17", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension18": "Dimension 18", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension19": "Dimension 19", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__dimension20": "Dimension 20", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__employee": "Employee", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSite": "Financial site", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteAmount": "Financial site amount", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__financialSiteCurrency": "Financial site currency", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__hasAttributesOrDimenionsChanged": "Has attributes or dimensions changed", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__item": "<PERSON><PERSON>", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__journalEntryLine": "Journal entry line", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__manufacturingSite": "Manufacturing site", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__project": "Project", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__stockSite": "Stock site", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedAttributes": "Stored attributes", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedComputedAttributes": "Stored computed attributes", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__storedDimensions": "Stored dimensions", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__supplier": "Supplier", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__task": "Task", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionAmount": "Transaction amount", "@sage/xtrem-finance/nodes__journal_entry_line_dimension__property__transactionCurrency": "Transaction currency", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__journal_entry_line_staging__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__journal_entry_line_staging__node_name": "Journal entry line staging", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__accountingStaging": "Accounting staging", "@sage/xtrem-finance/nodes__journal_entry_line_staging__property__journalEntryLine": "Journal entry line", "@sage/xtrem-finance/nodes__journal-entry__cant_post_journal_entry_when_status_is_not_draft_nor_error": "The posting status is not {{draft}} or {{error}}. The journal entry invoice cannot be posted.", "@sage/xtrem-finance/nodes__journal-entry__posted": "The journal entry was posted.", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__payment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment": "Void payment", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__failed": "Void payment failed.", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__payment": "Payment", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidDate": "Void date", "@sage/xtrem-finance/nodes__payment__mutation__voidPayment__parameter__voidText": "Void text", "@sage/xtrem-finance/nodes__payment__node_name": "Payment", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_mandatory": "You need to add a reference to the accounts payable invoice.", "@sage/xtrem-finance/nodes__payment_document_line__ap_invoice_reference_not_allowed": "You cannot reference the accounts payable invoice.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_mandatory": "You need to add a reference to the accounts receivable invoice.", "@sage/xtrem-finance/nodes__payment_document_line__ar_invoice_reference_not_allowed": "You cannot reference the accounts receivable invoice.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ap_invoice_posting_status": "The posting status of the accounts payable invoice needs to be posted.", "@sage/xtrem-finance/nodes__payment_document_line__wrong_ar_invoice_posting_status": "The posting status of the accounts receivable invoice needs to be posted.", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-finance/nodes__receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment": "Void payment", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__failed": "Void payment failed.", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__payment": "Payment", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidDate": "Void date", "@sage/xtrem-finance/nodes__receipt__mutation__voidPayment__parameter__voidText": "Void text", "@sage/xtrem-finance/nodes__receipt__node_name": "Receipt", "@sage/xtrem-finance/nodes__receipt__payment_amount_discrepancy": "The payment amount of the document: {{amount}}, needs to be the same as the total of payment amounts of all lines: {{total}}.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_error": "The forced payment for all open items failed. Review batch task logs for more information.", "@sage/xtrem-finance/notification_bulk_open_item_payment__description_success": "The open items were forced to be paid.", "@sage/xtrem-finance/package__name": "Finance", "@sage/xtrem-finance/page__void_date__mandatory": "The date is mandatory.", "@sage/xtrem-finance/page_fragments__base_payment__date_issued": "Date issued", "@sage/xtrem-finance/page_fragments__base_payment__date_received": "Date received", "@sage/xtrem-finance/page_fragments__payment_information__amount_in_bank_currency_must_be_positive": "The amount in bank currency needs to be greater than 0.", "@sage/xtrem-finance/page_fragments__payment_information__date_issued": "Date issued", "@sage/xtrem-finance/page_fragments__payment_information__date_received": "Date received", "@sage/xtrem-finance/page_fragments__payment_information__payment_amount_can_not_be_negative": "The payment amount needs to be greater than or equal to 0.", "@sage/xtrem-finance/page_fragments__payment_information__transaction_information_can_not_exceed_100_characters": "Transaction information cannot exceed 100 characters.", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionCriteriaBlock____title": "Criteria", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionDocumentType____title": "Document type", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___createStamp": "<PERSON><PERSON>", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title___id": "ID", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__batchId": "Batch ID", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentNumber": "Document number", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__documentType": "Document type", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__message": "Message", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentNumber": "Document number", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__originDocumentType": "Document type", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__postingStatus": "Status", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__status": "Notification status", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentNumber": "Target document number", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__targetDocumentType": "Target document type", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____columns__title__wasResent": "Resent", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title": "Edit", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__2": "Edit source document dimensions", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____dropdownActions__title__3": "Retry", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionHistory____title": "Results", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionSection____title": "Finance", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__financeTransactionStatus____title": "Status", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentNumber": "Number", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____columns__title__sourceDocumentType": "Document", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocuments____title": "Source documents", "@sage/xtrem-finance/page-extensions__sys_notification_history_extension__sourceDocumentsBlock____title": "Source documents", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__adjustmentAmount": "Adjustment amount", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__amount": "Amount", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__discountAmount": "Discount amount", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__bankAccount__name": "Bank account", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__isVoided": "Voided", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__number": "Payment number", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentDate": "Payment date", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__paymentMethod": "Payment method", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__document__reference": "Transaction information", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____columns__title__penaltyAmount": "Penalty amount", "@sage/xtrem-finance/page-fragments__accounts_payable_open_item_payment__payments____title": "Payments", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__adjustmentAmount": "Adjustment amount", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__amount": "Amount", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__discountAmount": "Discount amount", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__bankAccount__name": "Bank account", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__isVoided": "Voided", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__number": "Receipt number", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentDate": "Receipt date", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__paymentMethod": "Payment method", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__document__reference": "Transaction information", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____columns__title__penaltyAmount": "Penalty amount", "@sage/xtrem-finance/page-fragments__accounts_receivable_open_item_payment__receipts____title": "Receipts", "@sage/xtrem-finance/page-fragments__base_payment__amount____title": "Payment amount", "@sage/xtrem-finance/page-fragments__base_payment__amountBankCurrency____title": "Amount in bank currency", "@sage/xtrem-finance/page-fragments__base_payment__bankAccount____title": "Bank account", "@sage/xtrem-finance/page-fragments__base_payment__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-finance/page-fragments__base_payment__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment__customer____title": "Customer", "@sage/xtrem-finance/page-fragments__base_payment__financialSite____title": "Financial site", "@sage/xtrem-finance/page-fragments__base_payment__isVoided____title": "Voided", "@sage/xtrem-finance/page-fragments__base_payment__number____title": "Number", "@sage/xtrem-finance/page-fragments__base_payment__paymentDate____title": "Date received", "@sage/xtrem-finance/page-fragments__base_payment__paymentMethod____title": "Payment method", "@sage/xtrem-finance/page-fragments__base_payment__reference____title": "Transaction information", "@sage/xtrem-finance/page-fragments__base_payment__supplier____title": "Supplier", "@sage/xtrem-finance/page-fragments__base_payment__voidDate____title": "Voided on", "@sage/xtrem-finance/page-fragments__base_payment__voidText____title": "Void text", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__adjustmentAmount": "Adjustment amount", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__dueDate": "Due date", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__postingDate": "Posting date", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "Document number", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__apOpenItem__accountsPayableInvoice__reference": "Reference", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__dueDate": "Due date", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Posting date", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "Reference", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "Document number", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__discountAmount": "Discount amount", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__origin": "Origin", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__penaltyAmount": "Penalty amount", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmount": "Amount", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____columns__title__signedAmountBankCurrency": "Amount in bank currency", "@sage/xtrem-finance/page-fragments__base_payment_line__lines____title": "Lines", "@sage/xtrem-finance/page-fragments__open_item_discount__discountDate____title": "Discount date", "@sage/xtrem-finance/page-fragments__open_item_discount__discountType____title": "Discount type", "@sage/xtrem-finance/page-fragments__open_item_discount__displayDiscountPaymentDate____title": "Discount payment before date", "@sage/xtrem-finance/page-fragments__open_item_general__businessRelation____title": "Business relation", "@sage/xtrem-finance/page-fragments__open_item_general__companyAmountDueSigned____title": "Company amount due", "@sage/xtrem-finance/page-fragments__open_item_general__documentNumberLink____title": "Document number", "@sage/xtrem-finance/page-fragments__open_item_general__documentType____title": "Document type", "@sage/xtrem-finance/page-fragments__open_item_general__dueDate____title": "Due date", "@sage/xtrem-finance/page-fragments__open_item_general__financialSite____title": "Financial site", "@sage/xtrem-finance/page-fragments__open_item_general__transactionAmountDueSigned____title": "Transaction amount due", "@sage/xtrem-finance/page-fragments__open_item_penalty__penaltyPaymentType____title": "Penalty type", "@sage/xtrem-finance/page-fragments__payment_information__amount____title": "Payment amount", "@sage/xtrem-finance/page-fragments__payment_information__amountBankCurrency____title": "Amount in bank currency", "@sage/xtrem-finance/page-fragments__payment_information__bankAccount____title": "Bank account", "@sage/xtrem-finance/page-fragments__payment_information__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-finance/page-fragments__payment_information__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__country__name": "Country", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__id": "ID", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__name": "Name", "@sage/xtrem-finance/page-fragments__payment_information__customer____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-finance/page-fragments__payment_information__customer____title": "Customer", "@sage/xtrem-finance/page-fragments__payment_information__date____title": "Date received", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____lookupDialogTitle": "Select site", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____placeholder": "Financial site", "@sage/xtrem-finance/page-fragments__payment_information__financialSite____title": "Financial site", "@sage/xtrem-finance/page-fragments__payment_information__paymentMethod____title": "Payment method", "@sage/xtrem-finance/page-fragments__payment_information__reference____title": "Transaction information", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__country__name": "Country", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__id": "ID", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__name": "Name", "@sage/xtrem-finance/page-fragments__payment_information__supplier____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-finance/page-fragments__payment_information__supplier____title": "Supplier", "@sage/xtrem-finance/page-fragments__payment_information__type____title": "Type", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__number": "Invoice number", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__origin": "Origin", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__postingDate": "Posting date", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsPayableInvoice__reference": "Reference", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__number": "Invoice number", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__origin": "Origin", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__postingDate": "Posting date", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__accountsReceivableInvoice__reference": "Reference", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__adjustmentAmount": "Adjustment amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__amountDue": "Amount due", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__creditAmount": "Credit amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__discountPaymentAmount": "Discount amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__dueDate": "Due date", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__paymentAmount": "Payment amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__penaltyPaymentAmount": "Penalty amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__remainingCompanyAmount": "Company amount due", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalAmount": "Total amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmount": "Total company amount", "@sage/xtrem-finance/page-fragments__record_lines__lines____columns__title__totalCompanyAmountPaid": "Company amount paid", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__id__title": "Bill-by supplier ID", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_4__title": "Type", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line_5__title": "Invoice date", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line10__title": "Total excluding tax", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line11__title": "Adjusted total tax", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line12__title": "Total tax", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line13__title": "Supplier invoice date", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line14__title": "Supplier document number", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line15__title": "Due date", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line2__title": "Bill-by supplier", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line6__title": "Financial site", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line7__title": "Total including tax", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line8__title": "Reference", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__line9__title": "Origin", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__listItem__titleRight__title": "Posting status", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__2": "Draft", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__3": "Posted", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__4": "In progress", "@sage/xtrem-finance/pages__accounts_payable_invoice____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypePlural": "Accounts payable invoices", "@sage/xtrem-finance/pages__accounts_payable_invoice____objectTypeSingular": "Accounts payable invoice", "@sage/xtrem-finance/pages__accounts_payable_invoice____title": "Accounts payable invoice", "@sage/xtrem-finance/pages__accounts_payable_invoice__billBySupplier____title": "Bill-by supplier", "@sage/xtrem-finance/pages__accounts_payable_invoice__dueDate____title": "Due date", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordId____title": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationAppRecordIdLink____title": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_payable_invoice__financeIntegrationStatus____title": "Accounting integration status", "@sage/xtrem-finance/pages__accounts_payable_invoice__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__accounts_payable_invoice__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_payable_invoice__goToSysNotificationPage____title": "Retry", "@sage/xtrem-finance/pages__accounts_payable_invoice__invoiceDate____title": "Invoice date", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amount": "Amount", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountExcludingTax": "", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__amountIncludingTax": "", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__financialSite__name": "Financial site", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineAmountExcludingTax": "Amount excluding tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineAmountIncludingTax": "Amount including tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__lineType": "Line type", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__recipientSite__name": "Receipt site", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxAmountAdjusted": "Tax amount", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__columns__title__taxDate": "Tax date", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____levels__dropdownActions__title": "Tax details", "@sage/xtrem-finance/pages__accounts_payable_invoice__lines____title": "Lines", "@sage/xtrem-finance/pages__accounts_payable_invoice__number____title": "Number", "@sage/xtrem-finance/pages__accounts_payable_invoice__origin____title": "Origin", "@sage/xtrem-finance/pages__accounts_payable_invoice__paymentStatus____title": "Payment status", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDate____title": "Posting date", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingDetails____title": "Posting", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingMessageBlock____title": "Error details", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingSection____title": "Posting", "@sage/xtrem-finance/pages__accounts_payable_invoice__postingStatus____title": "Posting status", "@sage/xtrem-finance/pages__accounts_payable_invoice__rateDescription____title": "Rate", "@sage/xtrem-finance/pages__accounts_payable_invoice__reference____title": "Reference", "@sage/xtrem-finance/pages__accounts_payable_invoice__status____title": "Status", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentDate____title": "Supplier invoice date", "@sage/xtrem-finance/pages__accounts_payable_invoice__supplierDocumentNumber____title": "Supplier invoice number", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__amount": "Amount", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Financial site", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__recipientSite__name": "Recipient site", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Tax detail", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Tax amount", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetailLines____title": "Tax detail lines", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__tax": "Tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmount": "Amount", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxAmountAdjusted": "Adjusted amount", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxCategory": "Category", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____columns__title__taxRate": "Rate", "@sage/xtrem-finance/pages__accounts_payable_invoice__taxDetails____title": "Totals", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountExcludingTax____title": "Calculated total excluding tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalAmountIncludingTax____title": "Calculated total including tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSection____title": "Totals", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalsSectionTaxTotalsBlock____title": "", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmount____title": "Calculated total tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__totalTaxAmountAdjusted____title": "Adjusted calculated total tax", "@sage/xtrem-finance/pages__accounts_payable_invoice__type____title": "Type", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__businessEntityId__title": "Supplier ID", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeReason__title": "Close reason", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__closeText__title": "Close text", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "Company amount due", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyAmountPaid__title": "Company amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__companyCurrency__title": "Company currency", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__documentType__title": "Document type", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__financialSite__title": "Financial site", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2__title": "Supplier name", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__line2Right__title": "Due date", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "Remaining company amount", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Remaining transaction amount", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__title__title": "Document number", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaction amount due", "@sage/xtrem-finance/pages__accounts_payable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Transaction amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypePlural": "Accounts payable open items", "@sage/xtrem-finance/pages__accounts_payable_open_item____objectTypeSingular": "Accounts payable open item", "@sage/xtrem-finance/pages__accounts_payable_open_item____title": "Accounts payable open item", "@sage/xtrem-finance/pages__accounts_payable_open_item__forcedAmountPaidSigned____title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_payable_open_item__status____title": "Status", "@sage/xtrem-finance/pages__accounts_payable_open_item__transactionAmountPaidSigned____title": "Total amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__bulkActions__title": "Close open items", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "Supplier ID", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeReason__title": "Close reason", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__closeText__title": "Close text", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "Company amount due", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "Company amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "Company currency", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Discount payment before date", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__documentType__title": "Document type", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__financialSite__title": "Financial site", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2__title": "Supplier name", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__line2Right__title": "Due date", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "Remaining company amount", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Remaining transaction amount", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__title__title": "Document number", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaction amount due", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Transaction amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title": "Not fully paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__3": "Not paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__4": "Partially paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____navigationPanel__optionsMenu__title__5": "Paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypePlural": "Initialize accounts payable payments", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____objectTypeSingular": "Initialize accounts payable payment", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay____title": "Initialize accounts payable payment", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeReason____title": "Close reason", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__closeText____title": "Close text", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__forcedAmountPaidSigned____title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__save____title": "Save", "@sage/xtrem-finance/pages__accounts_payable_open_item_pay__transactionAmountPaidSigned____title": "Total amount paid", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line_4__title": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line13__title": "Payment date", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line2Right__title": "Posting date", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line7__title": "Pay-to customer", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line8__title": "Advance amount", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__line9__title": "Reference", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__listItem__titleRight__title": "Posting status", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__2": "Draft", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__3": "Posted", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__4": "In progress", "@sage/xtrem-finance/pages__accounts_receivable_advance____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypePlural": "Accounts receivable advances", "@sage/xtrem-finance/pages__accounts_receivable_advance____objectTypeSingular": "Accounts receivable advance", "@sage/xtrem-finance/pages__accounts_receivable_advance____title": "Accounts receivable advance", "@sage/xtrem-finance/pages__accounts_receivable_advance__advanceAmount____title": "Advance amount", "@sage/xtrem-finance/pages__accounts_receivable_advance__bankAccount____title": "Bank", "@sage/xtrem-finance/pages__accounts_receivable_advance__description____title": "Description", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordId____title": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationAppRecordIdLink____title": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_receivable_advance__financeIntegrationStatus____title": "Accounting integration status", "@sage/xtrem-finance/pages__accounts_receivable_advance__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_advance__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__account__composedDescription": "Account", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__advanceAmount": "Amount", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____columns__title__description": "Description", "@sage/xtrem-finance/pages__accounts_receivable_advance__lines____title": "Lines", "@sage/xtrem-finance/pages__accounts_receivable_advance__number____title": "Number", "@sage/xtrem-finance/pages__accounts_receivable_advance__paymentDate____title": "Payment date", "@sage/xtrem-finance/pages__accounts_receivable_advance__payToCustomerName____title": "Pay-to customer", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingDate____title": "Receipt date", "@sage/xtrem-finance/pages__accounts_receivable_advance__postingStatus____title": "Posting status", "@sage/xtrem-finance/pages__accounts_receivable_advance__reference____title": "Reference", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__billToCustomerId__title": "Bill-to customer ID", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_4__title": "Type", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line_5__title": "Invoice date", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line10__title": "Origin", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line11__title": "Total excluding tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line12__title": "Adjusted total tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line13__title": "Due date", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line2Right__title": "Posting date", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line6__title": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line7__title": "Bill-to customer", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line8__title": "Total including tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__line9__title": "Reference", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__listItem__titleRight__title": "Posting status", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__2": "Draft", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__3": "Posted", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__4": "In progress", "@sage/xtrem-finance/pages__accounts_receivable_invoice____navigationPanel__optionsMenu__title__5": "Error", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypePlural": "Accounts receivable invoices", "@sage/xtrem-finance/pages__accounts_receivable_invoice____objectTypeSingular": "Accounts receivable invoice", "@sage/xtrem-finance/pages__accounts_receivable_invoice____title": "Accounts receivable invoice", "@sage/xtrem-finance/pages__accounts_receivable_invoice__billToCustomer____title": "Bill-to customer", "@sage/xtrem-finance/pages__accounts_receivable_invoice__dueDate____title": "Due date", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordId____title": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationAppRecordIdLink____title": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financeIntegrationStatus____title": "Accounting integration status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_invoice__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_invoice__goToSysNotificationPage____title": "Retry", "@sage/xtrem-finance/pages__accounts_receivable_invoice__invoiceDate____title": "Invoice date", "@sage/xtrem-finance/pages__accounts_receivable_invoice__isPrinted____title": "Printed", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amount": "Amount", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountExcludingTax": "", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__amountIncludingTax": "", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__financialSite__name": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineAmountExcludingTax": "Amount excluding tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineAmountIncludingTax": "Amount including tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__lineType": "Line type", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__providerSite__name": "Provider site", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxAmount": "Tax amount", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__columns__title__taxDate": "Tax date", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____levels__dropdownActions__title": "Tax details", "@sage/xtrem-finance/pages__accounts_receivable_invoice__lines____title": "Lines", "@sage/xtrem-finance/pages__accounts_receivable_invoice__number____title": "Number", "@sage/xtrem-finance/pages__accounts_receivable_invoice__origin____title": "Origin", "@sage/xtrem-finance/pages__accounts_receivable_invoice__paymentStatus____title": "Payment status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDate____title": "Posting date", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingDetails____title": "Posting", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingMessageBlock____title": "Error details", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingSection____title": "Posting", "@sage/xtrem-finance/pages__accounts_receivable_invoice__postingStatus____title": "Posting status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__rateDescription____title": "Rate", "@sage/xtrem-finance/pages__accounts_receivable_invoice__reference____title": "Reference", "@sage/xtrem-finance/pages__accounts_receivable_invoice__status____title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__amount": "Amount", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__financialSite__name": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__providerSite__name": "Provider site", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxDetail": "Tax detail", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____levels__columns__title__taxLineTaxAmount": "Tax amount", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetailLines____title": "Tax detail lines", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__postfix__taxRate": "%", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__tax": "Tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxAmount": "Amount", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxCategory": "Category", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____columns__title__taxRate": "Rate", "@sage/xtrem-finance/pages__accounts_receivable_invoice__taxDetails____title": "Totals", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountExcludingTax____title": "Total excluding tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalAmountIncludingTax____title": "Total including tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSection____title": "Totals", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalsSectionTaxTotalsBlock____title": "Totals", "@sage/xtrem-finance/pages__accounts_receivable_invoice__totalTaxAmount____title": "Total tax", "@sage/xtrem-finance/pages__accounts_receivable_invoice__type____title": "Type", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__businessEntityId__title": "Customer ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeReason__title": "Close reason", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__closeText__title": "Close text", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountDueSigned__title": "Company amount due", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyAmountPaid__title": "Company amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__companyCurrency__title": "Company currency", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__displayDiscountPaymentDate__title": "Discount payment before date", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__documentType__title": "Document type", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__financialSite__title": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__forcedAmountPaid__title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2__title": "Customer name", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__line2Right__title": "Due date", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingCompanyAmount__title": "Remaining company amount", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__remainingTransactionAmount__title": "Remaining transaction amount", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__title__title": "Document number", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaction amount due", "@sage/xtrem-finance/pages__accounts_receivable_open_item____navigationPanel__listItem__transactionAmountPaid__title": "Transaction amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypePlural": "Accounts receivable open items", "@sage/xtrem-finance/pages__accounts_receivable_open_item____objectTypeSingular": "Accounts receivable open item", "@sage/xtrem-finance/pages__accounts_receivable_open_item____title": "Accounts receivable open item", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__id": "ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____columns__title__name": "Name", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeReason____title": "Close reason", "@sage/xtrem-finance/pages__accounts_receivable_open_item__closeText____title": "Close text", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_amount": "Discount amount", "@sage/xtrem-finance/pages__accounts_receivable_open_item__discount_percentage": "Discount percent", "@sage/xtrem-finance/pages__accounts_receivable_open_item__forcedAmountPaidSigned____title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_amount": "Penalty amount", "@sage/xtrem-finance/pages__accounts_receivable_open_item__penalty_percentage": "Penalty percent", "@sage/xtrem-finance/pages__accounts_receivable_open_item__transactionAmountPaidSigned____title": "Total amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__bulkActions__title": "Close open items", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__businessEntityId__title": "Customer ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeReason__title": "Close reason", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__closeText__title": "Close text", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountDueSigned__title": "Company amount due", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyAmountPaid__title": "Company amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__companyCurrency__title": "Company currency", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__displayDiscountPaymentDate__title": "Discount payment before date", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__documentType__title": "Document type", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__financialSite__title": "Financial site", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__forcedAmountPaid__title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2__title": "Customer name", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__line2Right__title": "Due date", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingCompanyAmount__title": "Remaining company amount", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__remainingTransactionAmount__title": "Remaining transaction amount", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__title__title": "Document number", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountDueSigned__title": "Transaction amount due", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__listItem__transactionAmountPaid__title": "Transaction amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title": "Not fully paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__3": "Not paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__4": "Partially paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____navigationPanel__optionsMenu__title__5": "Paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypePlural": "Initialize accounts receivable payments", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____objectTypeSingular": "Initialize accounts receivable payment", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay____title": "Initialize accounts receivable payment", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__id": "ID", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____columns__title__name": "Name", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeReason____title": "Close reason", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__closeText____title": "Close text", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_negative": "The forced amount paid needs to be positive.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forced_amount_paid_wrongly_positive": "The forced amount paid needs to be negative.", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__forcedAmountPaidSigned____title": "Forced amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__generalSection____title": "General", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__save____title": "Save", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__transactionAmountPaidSigned____title": "Total amount paid", "@sage/xtrem-finance/pages__accounts_receivable_open_item_pay__wrong_forced_amount_paid": "The forced amount paid needs to be between 0 and {{maxForcedAmount}}.", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title": "Extract", "@sage/xtrem-finance/pages__datev_export____navigationPanel__dropdownActions__title__2": "Export", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevConsultantNumber__title": "Consultant number", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__datevcustomerNumber__title": "Customer number", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doAccounts__title": "Accounts", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doCustomersSuppliers__title": "Customers and suppliers", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__doJournalEntries__title": "Journal entries", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__fiscalYearStart__title": "Fiscal year start", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__id__title": "ID", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__isLocked__title": "Locked", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2__title": "Start date", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__line2Right__title": "End date", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__timeStamp__title": "Extracted on", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__title__title": "Company", "@sage/xtrem-finance/pages__datev_export____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__datev_export____objectTypePlural": "DATEV exports", "@sage/xtrem-finance/pages__datev_export____objectTypeSingular": "DATEV export", "@sage/xtrem-finance/pages__datev_export____title": "DATEV export", "@sage/xtrem-finance/pages__datev_export__accountsSection____title": "Extracted accounts", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__accountsWithoutDatevId____title": "Accounts without DATEV ID", "@sage/xtrem-finance/pages__datev_export__acountsWithoutDatevIdSection____title": "Accounts without DATEV ID", "@sage/xtrem-finance/pages__datev_export__businessRelationsSection____title": "Extracted customers and suppliers", "@sage/xtrem-finance/pages__datev_export__createDatevExport____title": "Create", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevId____title": "Customers without DATEV ID", "@sage/xtrem-finance/pages__datev_export__customersWithoutDatevIdSection____title": "Customers without DATEV ID", "@sage/xtrem-finance/pages__datev_export__datevConsultantNumber____title": "DATEV consultant number", "@sage/xtrem-finance/pages__datev_export__datevCustomerNumber____title": "DATEV customer number", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__account__id": "ID", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__datevId": "DATEV ID", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__datevExportAccounts____title": "Extracted accounts", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__businessRelation__id": "ID", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__city": "City", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__country__name": "Country", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__datevId": "DATEV ID", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__postcode": "Postal code", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__street": "Street", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-finance/pages__datev_export__datevExportBusinessRelations____title": "Extracted customers and suppliers", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__businessEntityTaxIdNumber": "Business entity tax ID", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyFxRate": "Rate", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__companyValue": "Amount in company currency", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevAccountId": "DATEV account ID", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevContraAccountId": "DATEV contra account ID", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__datevSign": "Sign", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__description": "Description", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension1": "Dimension 1", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__dimension2": "Dimension 2", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__locked": "Locked", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__number": "Number", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingDate": "Posting date", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__postingKey": "Posting key", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__siteTaxIdNumber": "Site tax ID", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____columns__title__transactionValue": "Transaction amount", "@sage/xtrem-finance/pages__datev_export__datevExportJournalEntryLines____title": "Extracted journal entry lines", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText1____title": "Dimension type 1", "@sage/xtrem-finance/pages__datev_export__dimensionTypeText2____title": "Dimension type 2", "@sage/xtrem-finance/pages__datev_export__doAccounts____title": "Export accounts", "@sage/xtrem-finance/pages__datev_export__doCustomersSuppliers____title": "Export customers and suppliers", "@sage/xtrem-finance/pages__datev_export__doJournalEntries____title": "Export journal entries", "@sage/xtrem-finance/pages__datev_export__endDate____title": "End date", "@sage/xtrem-finance/pages__datev_export__export____title": "Export", "@sage/xtrem-finance/pages__datev_export__extract____title": "Extract", "@sage/xtrem-finance/pages__datev_export__fiscalYearStart____title": "Fiscal year start", "@sage/xtrem-finance/pages__datev_export__headerSection____title": "Header section", "@sage/xtrem-finance/pages__datev_export__id____title": "ID", "@sage/xtrem-finance/pages__datev_export__isLocked____title": "Locked", "@sage/xtrem-finance/pages__datev_export__journalEntryLineSection____title": "Extracted journal entry lines", "@sage/xtrem-finance/pages__datev_export__notification_export_sent": "DATEV export request sent.", "@sage/xtrem-finance/pages__datev_export__notification_success": "DATEV extraction request sent.", "@sage/xtrem-finance/pages__datev_export__skrCoaString____title": "SKR", "@sage/xtrem-finance/pages__datev_export__startDate____title": "Start date", "@sage/xtrem-finance/pages__datev_export__status____title": "Status", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevId____title": "Suppliers without DATEV ID", "@sage/xtrem-finance/pages__datev_export__suppliersWithoutDatevIdSection____title": "Suppliers without DATEV ID", "@sage/xtrem-finance/pages__datev_export__timeStamp____title": "Last extraction date", "@sage/xtrem-finance/pages__datev_export_parameters__datev_configuration": "DATEV configuration", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_date_range": "The end date needs to be after the start date.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_dimension_types": "The dimension type 2 needs to be different from the dimension type 1.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_date": "The end date needs to be less than one year after the start of the fiscal year.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_end_for_begin_of_fiscal_year": "The end date needs to be after the start of the fiscal year.", "@sage/xtrem-finance/pages__datev_export_parameters__wrong_start_for_begin_of_fiscal_year": "The start date needs to be after the start of the fiscal year.", "@sage/xtrem-finance/pages__datev_export_settings____title": "DATEV export settings", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__id": "ID", "@sage/xtrem-finance/pages__datev_export_settings__company____columns__title__name": "Name", "@sage/xtrem-finance/pages__datev_export_settings__criteriaBlock____title": "Criteria", "@sage/xtrem-finance/pages__datev_export_settings__dateRange____title": "Date range", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute1____title": "Dimension type 1", "@sage/xtrem-finance/pages__datev_export_settings__dimensionTypeOrAttribute2____title": "Dimension type 2", "@sage/xtrem-finance/pages__datev_export_settings__doAccounts____title": "Export accounts", "@sage/xtrem-finance/pages__datev_export_settings__doCustomersSuppliers____title": "Export customers and suppliers", "@sage/xtrem-finance/pages__datev_export_settings__doJournalEntries____title": "Export journal entries", "@sage/xtrem-finance/pages__datev_export_settings__endDate____title": "End date", "@sage/xtrem-finance/pages__datev_export_settings__fiscalYearStart____title": "Fiscal year start", "@sage/xtrem-finance/pages__datev_export_settings__id____title": "ID", "@sage/xtrem-finance/pages__datev_export_settings__isLocked____title": "Locked", "@sage/xtrem-finance/pages__datev_export_settings__mainSection____title": "General", "@sage/xtrem-finance/pages__datev_export_settings__startDate____title": "Start date", "@sage/xtrem-finance/pages__generate_journal_entries____title": "Generate journal entries", "@sage/xtrem-finance/pages__generate_journal_entries__create_button_text": "Generate", "@sage/xtrem-finance/pages__generate_journal_entries__dateFrom____title": "Start date", "@sage/xtrem-finance/pages__generate_journal_entries__dateTo____title": "End date", "@sage/xtrem-finance/pages__generate_journal_entries__documentType____title": "Document type", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____lookupDialogTitle": "Select financial site", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____placeholder": "Select ...", "@sage/xtrem-finance/pages__generate_journal_entries__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__generate_journal_entries__generalBlock____title": "Criteria", "@sage/xtrem-finance/pages__generate_journal_entries__generalSection____title": "General", "@sage/xtrem-finance/pages__generate_journal_entries__recurring": "Recurring", "@sage/xtrem-finance/pages__generate_journal_entries__run_once": "Run once", "@sage/xtrem-finance/pages__generate_journal_entries__schedule_button_text": "Schedule", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_4__title": "Journal", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line_5__title": "Document type", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line2Right__title": "Posting date", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line3__title": "Financial site", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line6__title": "Reference", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__line7__title": "Origin", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-finance/pages__journal_entry____navigationPanel__listItem__titleRight__title": "Posting status", "@sage/xtrem-finance/pages__journal_entry____objectTypePlural": "Journal entries", "@sage/xtrem-finance/pages__journal_entry____objectTypeSingular": "Journal entry", "@sage/xtrem-finance/pages__journal_entry____title": "Journal entry", "@sage/xtrem-finance/pages__journal_entry___id____title": "ID", "@sage/xtrem-finance/pages__journal_entry__description____title": "Description", "@sage/xtrem-finance/pages__journal_entry__documentType____title": "Document type", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordId____title": "Accounting integration reference", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationAppRecordIdLink____title": "Accounting integration reference", "@sage/xtrem-finance/pages__journal_entry__financeIntegrationStatus____title": "Accounting integration status", "@sage/xtrem-finance/pages__journal_entry__financialSite____columns__lookupDialogTitle__legalCompany__name": "Select company", "@sage/xtrem-finance/pages__journal_entry__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__journal_entry__generalSection____title": "General", "@sage/xtrem-finance/pages__journal_entry__journal____title": "Journal", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__account__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension01__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension02__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension03__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension04__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension05__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension06__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension07__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension08__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension09__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension10__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension11__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension12__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension13__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension14__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension15__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension16__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension17__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension18__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension19__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__dimension20__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__employee__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title": "Name", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__columns__project__composedDescription__title__2": "ID", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__account__composedDescription": "Account", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__businessEntity__name": "Business entity", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__commonReference": "Common reference", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyAmount": "Company amount", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyCredit": "Company credit", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__companyDebit": "Company debit", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__contraAccount": "Contra account", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__description": "Description", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__dueDate": "Due date", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__financialSite__name": "Financial site", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__fxRateDate": "Exchange rate date", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__rateDescription": "Exchange rate", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__tax__name": "Tax", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__taxDate": "Tax date", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionAmount": "Transaction amount", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionCredit": "Transaction credit", "@sage/xtrem-finance/pages__journal_entry__lines____levels__columns__title__transactionDebit": "Transaction debit", "@sage/xtrem-finance/pages__journal_entry__lines____levels__dropdownActions__title": "Tax details", "@sage/xtrem-finance/pages__journal_entry__lines____title": "Lines", "@sage/xtrem-finance/pages__journal_entry__number____title": "Number", "@sage/xtrem-finance/pages__journal_entry__origin____title": "Origin", "@sage/xtrem-finance/pages__journal_entry__postingDate____title": "Posting date", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentNumber": "Document number", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__documentType": "Document type", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-finance/pages__journal_entry__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-finance/pages__journal_entry__postingDetails____title": "Posting", "@sage/xtrem-finance/pages__journal_entry__postingMessageBlock____title": "Error details", "@sage/xtrem-finance/pages__journal_entry__postingSection____title": "Posting", "@sage/xtrem-finance/pages__journal_entry__postingSectionBlock____title": "Posting", "@sage/xtrem-finance/pages__journal_entry__postingStatus____title": "Posting status", "@sage/xtrem-finance/pages__journal_entry__reference____title": "Reference", "@sage/xtrem-finance/pages__journal_entry__status____title": "Status", "@sage/xtrem-finance/pages__journal_entry_inquiry____title": "Journal entry inquiry", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____lookupDialogTitle": "Select companies", "@sage/xtrem-finance/pages__journal_entry_inquiry__companyFilter____title": "Companies", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateFrom____title": "Start date", "@sage/xtrem-finance/pages__journal_entry_inquiry__dateTo____title": "End date", "@sage/xtrem-finance/pages__journal_entry_inquiry__filtersBlock____title": "Criteria", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__id": "Account code", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__account__name": "Account name", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank": "Matching", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__blank__2": "Matching date", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__id": "Business entity code", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__businessEntity__name": "Business entity name", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__commonReference": "Common reference", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__companyAmount": "Amount", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__contraAccount": "Contra account", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__dueDate": "Due date", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__id": "Site code", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__financialSite__legalCompany__name": "Company name", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryDescription": "Description", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__inquiryTransactionCurrency__id": "Transaction currency", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__id": "Journal code", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__journal__name": "Journal name", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__number": "Number", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate": "Posting date", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__postingDate__2": "Reference document date", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__journalEntry__reference": "Reference", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__numericSign": "Sign", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__signedTransactionAmount": "Transaction amount", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__tax__name": "Tax", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__taxDate": "Tax date", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____columns__title__validationDate": "Validation date", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalEntryLines____title": "Lines", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____lookupDialogTitle": "Select journals", "@sage/xtrem-finance/pages__journal_entry_inquiry__journalFilter____title": "Journals", "@sage/xtrem-finance/pages__journal_entry_inquiry__linesSection____title": "Lines", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amount__title": "Amount", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountBankCurrency__title": "Amount in bank currency", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__amountCompanyCurrency__title": "Amount in company currency", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankAccount__title": "Bank account", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__bankCurrency__title": "Bank account currency", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__businessRelationId__title": "Business entity ID", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__companyCurrency__title": "Company currency", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__financialSite__title": "Financial site", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__isVoided__title": "Voided", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2__title": "Business entity name", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__line2Right__title": "Payment date", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__paymentMethod__title": "Payment method", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__reference__title": "Reference", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidDate__title": "Voided on", "@sage/xtrem-finance/pages__payment____navigationPanel__listItem__voidText__title": "Void text", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title": "Posted payments", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__2": "Voided payments", "@sage/xtrem-finance/pages__payment____navigationPanel__optionsMenu__title__3": "All payments", "@sage/xtrem-finance/pages__payment____objectTypePlural": "Payments", "@sage/xtrem-finance/pages__payment____objectTypeSingular": "Payment", "@sage/xtrem-finance/pages__payment____title": "Payment", "@sage/xtrem-finance/pages__payment__cancel_void_button": "Cancel", "@sage/xtrem-finance/pages__payment__cancelPayment____title": "Cancel", "@sage/xtrem-finance/pages__payment__confirm_void_button": "Confirm", "@sage/xtrem-finance/pages__payment__createPayment____title": "Confirm", "@sage/xtrem-finance/pages__payment__linesSection____title": "Lines", "@sage/xtrem-finance/pages__payment__mainSection____title": "General", "@sage/xtrem-finance/pages__payment__paymentInformationBlock____title": "Payment information", "@sage/xtrem-finance/pages__payment__voidPayment____title": "Void", "@sage/xtrem-finance/pages__payment__voidPaymentDate____helperText": "Void the payment on date", "@sage/xtrem-finance/pages__payment__voidPaymentDate____title": "Date", "@sage/xtrem-finance/pages__payment__voidPaymentText____title": "Text", "@sage/xtrem-finance/pages__payment__voidSection____title": "Void payment", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amount__title": "Amount", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountBankCurrency__title": "Amount in bank currency", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__amountCompanyCurrency__title": "Amount in company currency", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankAccount__title": "Bank account", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__bankCurrency__title": "Bank account currency", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__businessRelationId__title": "Business entity ID", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__companyCurrency__title": "Company currency", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__currency__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__financialSite__title": "Financial site", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__isVoided__title": "Voided", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2__title": "Business entity name", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__line2Right__title": "Payment date", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__paymentMethod__title": "Payment method", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__reference__title": "Reference", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidDate__title": "Voided on", "@sage/xtrem-finance/pages__receipt____navigationPanel__listItem__voidText__title": "Void text", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title": "Posted receipts", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__2": "Voided receipts", "@sage/xtrem-finance/pages__receipt____navigationPanel__optionsMenu__title__3": "All receipts", "@sage/xtrem-finance/pages__receipt____objectTypePlural": "Receipts", "@sage/xtrem-finance/pages__receipt____objectTypeSingular": "Receipt", "@sage/xtrem-finance/pages__receipt____title": "Receipt", "@sage/xtrem-finance/pages__receipt__amount____title": "Payment amount", "@sage/xtrem-finance/pages__receipt__amountBankCurrency____title": "Amount in bank currency", "@sage/xtrem-finance/pages__receipt__bankAccount____title": "Bank account", "@sage/xtrem-finance/pages__receipt__cancel_void": "Cancel", "@sage/xtrem-finance/pages__receipt__cancelReceipt____title": "Cancel", "@sage/xtrem-finance/pages__receipt__cancelVoid____title": "Cancel", "@sage/xtrem-finance/pages__receipt__confirm_void": "Confirm", "@sage/xtrem-finance/pages__receipt__confirmVoid____title": "Confirm", "@sage/xtrem-finance/pages__receipt__createReceipt____title": "Confirm", "@sage/xtrem-finance/pages__receipt__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-finance/pages__receipt__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__customer____title": "Customer", "@sage/xtrem-finance/pages__receipt__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__receipt__isVoided____title": "Voided", "@sage/xtrem-finance/pages__receipt__lines____columns__title__apOpenItem__accountsPayableInvoice__purchaseDocumentNumber": "Document number", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__postingDate": "Posting date", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__reference": "Reference", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__accountsReceivableInvoice__salesDocumentNumber": "Document number", "@sage/xtrem-finance/pages__receipt__lines____columns__title__arOpenItem__dueDate": "Due date", "@sage/xtrem-finance/pages__receipt__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__receipt__lines____columns__title__origin": "Origin", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmount": "Amount", "@sage/xtrem-finance/pages__receipt__lines____columns__title__signedAmountBankCurrency": "Amount in bank currency", "@sage/xtrem-finance/pages__receipt__lines____title": "Lines", "@sage/xtrem-finance/pages__receipt__linesSection____title": "Lines", "@sage/xtrem-finance/pages__receipt__mainSection____title": "General", "@sage/xtrem-finance/pages__receipt__number____title": "Number", "@sage/xtrem-finance/pages__receipt__paymentDate____title": "Date received", "@sage/xtrem-finance/pages__receipt__paymentInformationBlock____title": "Payment information", "@sage/xtrem-finance/pages__receipt__paymentMethod____title": "Payment method", "@sage/xtrem-finance/pages__receipt__reference____title": "Transaction information", "@sage/xtrem-finance/pages__receipt__supplier____title": "Supplier", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____helperText": "Void the payment on date", "@sage/xtrem-finance/pages__receipt__voidPaymentDate____title": "Date", "@sage/xtrem-finance/pages__receipt__voidPaymentText____title": "Text", "@sage/xtrem-finance/pages__receipt__voidReceipt____title": "Void", "@sage/xtrem-finance/pages__receipt__voidSection____title": "Void receipt", "@sage/xtrem-finance/pages__record_payment____title": "Record payment", "@sage/xtrem-finance/pages__record_payment__amount____title": "Payment amount", "@sage/xtrem-finance/pages__record_payment__amount_in_bank_currency_must_be_positive": "The amount in the bank currency needs to be greater than 0.", "@sage/xtrem-finance/pages__record_payment__amountAvailableToApply____title": "Amount available to apply", "@sage/xtrem-finance/pages__record_payment__amountBankCurrency____title": "Amount in bank currency", "@sage/xtrem-finance/pages__record_payment__bankAccount____title": "Bank account", "@sage/xtrem-finance/pages__record_payment__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-finance/pages__record_payment__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__country__name": "Country", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__id": "ID", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__name": "Name", "@sage/xtrem-finance/pages__record_payment__customer____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-finance/pages__record_payment__customer____title": "Customer", "@sage/xtrem-finance/pages__record_payment__date____title": "Date issued", "@sage/xtrem-finance/pages__record_payment__financialSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-finance/pages__record_payment__financialSite____lookupDialogTitle": "Select site", "@sage/xtrem-finance/pages__record_payment__financialSite____placeholder": "Financial site", "@sage/xtrem-finance/pages__record_payment__financialSite____title": "Financial site", "@sage/xtrem-finance/pages__record_payment__generateButton____title": "Generate", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__number": "Invoice number", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__origin": "Origin", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__postingDate": "Posting date", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__accountsPayableInvoice__reference": "Reference", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__amountDue": "Amount due", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__creditAmount": "Credit amount", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__dueDate": "Due date", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__paymentAmount": "Payment amount", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__remainingCompanyAmount": "Company amount due", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalAmount": "Total amount", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmount": "Total company amount", "@sage/xtrem-finance/pages__record_payment__lines____columns__title__totalCompanyAmountPaid": "Company amount paid", "@sage/xtrem-finance/pages__record_payment__lines____title": "Results", "@sage/xtrem-finance/pages__record_payment__linesBlock____title": "Results", "@sage/xtrem-finance/pages__record_payment__linesSection____title": "Lines", "@sage/xtrem-finance/pages__record_payment__mainSection____title": "General", "@sage/xtrem-finance/pages__record_payment__payment_amount_can_not_be_negative": "The payment amount needs to be greater than or equal to 0.", "@sage/xtrem-finance/pages__record_payment__payment_created": "Payment created: {{paymentNumber}}.", "@sage/xtrem-finance/pages__record_payment__paymentInformationBlock____title": "Payment information", "@sage/xtrem-finance/pages__record_payment__paymentMethod____title": "Payment method", "@sage/xtrem-finance/pages__record_payment__reference____title": "Transaction information", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__country__name": "Country", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__id": "ID", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__name": "Name", "@sage/xtrem-finance/pages__record_payment__supplier____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-finance/pages__record_payment__supplier____title": "Supplier", "@sage/xtrem-finance/pages__record_payment__totalPaymentApplied____title": "Total payment applied", "@sage/xtrem-finance/pages__record_payment__transaction_information_can_not_exceed_100_characters": "The transaction information cannot exceed 100 characters.", "@sage/xtrem-finance/pages__record_payment__type____title": "Type", "@sage/xtrem-finance/pages__record_receipt____title": "Record receipt", "@sage/xtrem-finance/pages__record_receipt__amountAvailableToApply____title": "Amount available to apply", "@sage/xtrem-finance/pages__record_receipt__bank_amount_must_be_positive": "Enter an amount in bank currency greater than 0.", "@sage/xtrem-finance/pages__record_receipt__generateButton____title": "Generate", "@sage/xtrem-finance/pages__record_receipt__linesBlock____title": "Results", "@sage/xtrem-finance/pages__record_receipt__linesSection____title": "Lines", "@sage/xtrem-finance/pages__record_receipt__mainSection____title": "General", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_positive": "Enter a payment amount greater than 0.", "@sage/xtrem-finance/pages__record_receipt__payment_amount_must_be_total_payment_applied": "The payment amount needs to be the same as the total payment applied.", "@sage/xtrem-finance/pages__record_receipt__paymentInformationBlock____title": "Payment information", "@sage/xtrem-finance/pages__record_receipt__receipt_created": "The following receipt was created: {{receiptNumber}}.", "@sage/xtrem-finance/pages__record_receipt__totalPaymentApplied____title": "Total payment applied", "@sage/xtrem-finance/permission__accounting_integration__name": "Accounting integration", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging__name": "Create journals from accounting staging", "@sage/xtrem-finance/permission__create_journals_from_accounting_staging_job__name": "Create journals from accounting staging job", "@sage/xtrem-finance/permission__delete__name": "Delete", "@sage/xtrem-finance/permission__initialize_paid_amount__name": "Initialize paid amount", "@sage/xtrem-finance/permission__manage__name": "Manage", "@sage/xtrem-finance/permission__post__name": "Post", "@sage/xtrem-finance/permission__read__name": "Read", "@sage/xtrem-finance/permission__retry_finance_document__name": "Retry finance document", "@sage/xtrem-finance/permission__single_record__name": "Single record", "@sage/xtrem-finance/permission__void__name": "Void", "@sage/xtrem-finance/search": "Search", "@sage/xtrem-finance/source_document_type_not_supported": "Source document type not supported: {{sourceDocumentType}}.", "@sage/xtrem-finance/status_not_supported": "Status not supported: {{status}}.", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_error": "Force open item payment", "@sage/xtrem-finance/success_notification__bulk_open_item_payment_title_success": "Force open item payment", "@sage/xtrem-finance/success_notification_description__ar_open_items_link": "Open items", "@sage/xtrem-finance/sys__notification_history__search": "Search", "@sage/xtrem-finance/target_document_not_found": "{{sysId}} {{type}}: Target document not found.", "@sage/xtrem-finance/target_document_type_not_supported": "{{targetDocumentType}}: Target document type not supported.", "@sage/xtrem-finance/unexpected_error": "Unexpected error: {{retryFinanceDocumentResult}}", "@sage/xtrem-finance/widgets__finance_integration_health____callToActions__seeAll__title": "See all", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__documentNumber__title": "Sort by document number", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__status__title": "Sort by status", "@sage/xtrem-finance/widgets__finance_integration_health____dataDropdownMenu__orderBy__updateStamp__title": "Sort by last status update", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2__title": "Document type", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line2Right__title": "Last status update", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__line3__title": "Message", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetNumber__title": "Target document number", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__targetType__title": "Target document type", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__title__title": "Status", "@sage/xtrem-finance/widgets__finance_integration_health____rowDefinition__titleRight__title": "Document number", "@sage/xtrem-finance/widgets__finance_integration_health____title": "Finance transaction status"}